import requests
import json
from datetime import datetime
import os

class RasaService:
    def __init__(self, rasa_url=None):
        self.rasa_url = rasa_url or os.getenv('RASA_URL', 'http://localhost:5005')
        self.webhook_url = f"{self.rasa_url}/webhooks/rest/webhook"
        self.model_url = f"{self.rasa_url}/model"
        
    def send_message(self, message, sender_id, metadata=None):
        """Send message to <PERSON><PERSON> and get response"""
        try:
            payload = {
                "sender": sender_id,
                "message": message
            }
            
            if metadata:
                payload["metadata"] = metadata
            
            response = requests.post(
                self.webhook_url,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                rasa_responses = response.json()
                
                # Process Rasa responses
                processed_responses = []
                for rasa_response in rasa_responses:
                    processed_response = {
                        'text': rasa_response.get('text', ''),
                        'buttons': rasa_response.get('buttons', []),
                        'elements': rasa_response.get('elements', []),
                        'quick_replies': rasa_response.get('quick_replies', []),
                        'image': rasa_response.get('image'),
                        'attachment': rasa_response.get('attachment'),
                        'custom': rasa_response.get('custom', {})
                    }
                    processed_responses.append(processed_response)
                
                return {
                    'success': True,
                    'responses': processed_responses,
                    'intent': self._extract_intent(rasa_responses),
                    'confidence': self._extract_confidence(rasa_responses)
                }
            else:
                return {
                    'success': False,
                    'error': f'Rasa server error: {response.status_code}',
                    'responses': []
                }
                
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': 'Rasa server timeout',
                'responses': []
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': 'Cannot connect to Rasa server',
                'responses': []
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'Unexpected error: {str(e)}',
                'responses': []
            }
    
    def _extract_intent(self, rasa_responses):
        """Extract intent from Rasa responses"""
        for response in rasa_responses:
            if 'custom' in response and 'intent' in response['custom']:
                return response['custom']['intent']
        return None
    
    def _extract_confidence(self, rasa_responses):
        """Extract confidence from Rasa responses"""
        for response in rasa_responses:
            if 'custom' in response and 'confidence' in response['custom']:
                return response['custom']['confidence']
        return None
    
    def get_model_status(self):
        """Get Rasa model status"""
        try:
            response = requests.get(f"{self.rasa_url}/status", timeout=10)
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            print(f"Error getting Rasa status: {e}")
            return None
    
    def is_healthy(self):
        """Check if Rasa server is healthy"""
        try:
            response = requests.get(f"{self.rasa_url}/status", timeout=5)
            return response.status_code == 200
        except:
            return False

class FallbackService:
    """Fallback service when Rasa is not available"""
    
    def __init__(self):
        self.fallback_responses = {
            'greeting': [
                "Hello! I'm your academic assistant. How can I help you today?",
                "Hi there! I'm here to help with your academic questions.",
                "Welcome! I can assist you with course information, schedules, and more."
            ],
            'course_info': [
                "I can help you with course information. Please specify which course you're interested in.",
                "For course details, please provide the course code or name.",
                "I have information about various courses. Which one would you like to know about?"
            ],
            'schedule': [
                "I can help you with your class schedule. What specific information do you need?",
                "For schedule information, please let me know what you're looking for.",
                "I can provide schedule details. What would you like to know?"
            ],
            'assignment': [
                "I can help you with assignment information. Which course or assignment are you asking about?",
                "For assignment details, please specify the course or assignment name.",
                "I have assignment information available. What do you need to know?"
            ],
            'lecturer': [
                "I can provide lecturer contact information. Which lecturer are you looking for?",
                "For lecturer details, please specify the name or course.",
                "I have lecturer information available. Who would you like to contact?"
            ],
            'default': [
                "I'm sorry, I didn't quite understand that. Could you please rephrase your question?",
                "I'm here to help with academic questions. Could you be more specific?",
                "I can assist with course info, schedules, assignments, and lecturer contacts. What do you need help with?"
            ]
        }
    
    def get_fallback_response(self, message, intent=None):
        """Get fallback response based on message content"""
        message_lower = message.lower()
        
        # Simple keyword matching
        if any(word in message_lower for word in ['hello', 'hi', 'hey', 'good morning', 'good afternoon']):
            intent = 'greeting'
        elif any(word in message_lower for word in ['course', 'class', 'subject']):
            intent = 'course_info'
        elif any(word in message_lower for word in ['schedule', 'timetable', 'time']):
            intent = 'schedule'
        elif any(word in message_lower for word in ['assignment', 'homework', 'project', 'due']):
            intent = 'assignment'
        elif any(word in message_lower for word in ['lecturer', 'professor', 'teacher', 'instructor']):
            intent = 'lecturer'
        else:
            intent = 'default'
        
        responses = self.fallback_responses.get(intent, self.fallback_responses['default'])
        
        return {
            'success': True,
            'responses': [{'text': responses[0]}],
            'intent': intent,
            'confidence': 0.5,
            'fallback': True
        }

# Global service instances
rasa_service = RasaService()
fallback_service = FallbackService()
