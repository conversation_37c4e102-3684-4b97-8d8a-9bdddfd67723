import os
from flask import Flask, jsonify
from flask_cors import CORS
from flask_jwt_extended import J<PERSON><PERSON>anager
from flask_socketio import Socket<PERSON>
from datetime import timedelta
from dotenv import load_dotenv

# Import routes
from routes.auth import auth_bp
from routes.chat import chat_bp
from routes.academic import academic_bp

# Import database connection
from models.database import init_db

# Load environment variables
load_dotenv()

def create_app():
    app = Flask(__name__)
    
    # Configuration
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'jwt-secret-change-in-production')
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)
    app.config['MONGODB_URI'] = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/chatbot_db')
    app.config['RASA_URL'] = os.getenv('RASA_URL', 'http://localhost:5005')
    
    # Initialize extensions
    CORS(app, origins=['http://localhost:5173', 'http://localhost:3000'])
    jwt = JWTManager(app)
    socketio = SocketIO(app, cors_allowed_origins=['http://localhost:5173', 'http://localhost:3000'])
    
    # Initialize database
    init_db(app.config['MONGODB_URI'])
    
    # Register blueprints
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(chat_bp, url_prefix='/api/chat')
    app.register_blueprint(academic_bp, url_prefix='/api/academic')
    
    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({'error': 'Not found'}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return jsonify({'error': 'Internal server error'}), 500
    
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        return jsonify({'error': 'Token has expired'}), 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        return jsonify({'error': 'Invalid token'}), 401
    
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        return jsonify({'error': 'Authorization token is required'}), 401
    
    # Health check endpoint
    @app.route('/api/health')
    def health_check():
        return jsonify({
            'status': 'healthy',
            'message': 'Academic Chatbot API is running'
        })
    
    # Socket.IO events for real-time chat
    @socketio.on('connect')
    def handle_connect():
        print('Client connected')
    
    @socketio.on('disconnect')
    def handle_disconnect():
        print('Client disconnected')
    
    @socketio.on('join_conversation')
    def handle_join_conversation(data):
        conversation_id = data.get('conversation_id')
        if conversation_id:
            join_room(conversation_id)
            print(f'Client joined conversation: {conversation_id}')
    
    @socketio.on('leave_conversation')
    def handle_leave_conversation(data):
        conversation_id = data.get('conversation_id')
        if conversation_id:
            leave_room(conversation_id)
            print(f'Client left conversation: {conversation_id}')
    
    return app, socketio

if __name__ == '__main__':
    app, socketio = create_app()
    
    # Run the application
    debug_mode = os.getenv('FLASK_ENV') == 'development'
    socketio.run(
        app,
        host='0.0.0.0',
        port=5000,
        debug=debug_mode,
        allow_unsafe_werkzeug=True
    )
