from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId
from datetime import datetime
from models.database import get_db, insert_document, find_documents, find_document, update_document, delete_document
from services.rasa_service import rasa_service, fallback_service
from models.user import User

chat_bp = Blueprint('chat', __name__)

@chat_bp.route('', methods=['POST'])
@jwt_required()
def send_message():
    """Send message to chatbot and get response"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        message = data.get('message', '').strip()
        conversation_id = data.get('conversation_id')
        
        if not message:
            return jsonify({'error': 'Message is required'}), 400
        
        # Get user info for context
        user = User.find_by_id(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Create or get conversation
        if not conversation_id:
            conversation_id = create_conversation(user_id, "New Conversation")
        else:
            # Verify conversation belongs to user
            conversation = find_document('conversations', {
                '_id': ObjectId(conversation_id),
                'user_id': ObjectId(user_id)
            })
            if not conversation:
                return jsonify({'error': 'Conversation not found'}), 404
        
        # Save user message
        user_message_id = save_message(
            conversation_id=conversation_id,
            user_id=user_id,
            message=message,
            sender='user'
        )
        
        # Prepare metadata for Rasa
        metadata = {
            'user_id': user_id,
            'student_id': user['student_id'],
            'program': user['program'],
            'year': user['year'],
            'conversation_id': conversation_id
        }
        
        # Send message to Rasa
        rasa_response = rasa_service.send_message(
            message=message,
            sender_id=user_id,
            metadata=metadata
        )
        
        # If Rasa is not available, use fallback
        if not rasa_response['success']:
            print(f"Rasa error: {rasa_response['error']}")
            rasa_response = fallback_service.get_fallback_response(message)
        
        # Extract bot response text
        bot_response_text = ""
        if rasa_response['responses']:
            bot_response_text = rasa_response['responses'][0].get('text', 'I apologize, but I encountered an error processing your request.')
        
        if not bot_response_text:
            bot_response_text = "I apologize, but I encountered an error processing your request."
        
        # Save bot response
        bot_message_id = save_message(
            conversation_id=conversation_id,
            user_id=user_id,
            message=bot_response_text,
            sender='bot',
            intent=rasa_response.get('intent'),
            confidence=rasa_response.get('confidence'),
            metadata=rasa_response.get('responses', [])
        )
        
        # Update conversation last activity
        update_document('conversations', 
                       {'_id': ObjectId(conversation_id)}, 
                       {'last_activity': datetime.utcnow()})
        
        return jsonify({
            'response': bot_response_text,
            'conversation_id': conversation_id,
            'intent': rasa_response.get('intent'),
            'confidence': rasa_response.get('confidence'),
            'user_message_id': str(user_message_id),
            'bot_message_id': str(bot_message_id),
            'fallback': rasa_response.get('fallback', False)
        }), 200
        
    except Exception as e:
        print(f"Chat error: {e}")
        return jsonify({'error': 'Failed to process message'}), 500

@chat_bp.route('/history', methods=['GET'])
@jwt_required()
def get_chat_history():
    """Get chat history for a conversation"""
    try:
        user_id = get_jwt_identity()
        conversation_id = request.args.get('conversation_id')
        limit = int(request.args.get('limit', 50))
        
        if not conversation_id:
            return jsonify({'error': 'Conversation ID is required'}), 400
        
        # Verify conversation belongs to user
        conversation = find_document('conversations', {
            '_id': ObjectId(conversation_id),
            'user_id': ObjectId(user_id)
        })
        
        if not conversation:
            return jsonify({'error': 'Conversation not found'}), 404
        
        # Get messages
        messages = find_documents(
            'messages',
            {'conversation_id': ObjectId(conversation_id)},
            sort=[('timestamp', 1)],
            limit=limit
        )
        
        # Format messages
        formatted_messages = []
        for msg in messages:
            formatted_messages.append({
                'id': str(msg['_id']),
                'text': msg['message'],
                'sender': msg['sender'],
                'timestamp': msg['timestamp'].isoformat(),
                'intent': msg.get('intent'),
                'confidence': msg.get('confidence')
            })
        
        return jsonify({
            'messages': formatted_messages,
            'conversation': {
                'id': str(conversation['_id']),
                'title': conversation['title'],
                'created_at': conversation['created_at'].isoformat(),
                'last_activity': conversation['last_activity'].isoformat()
            }
        }), 200
        
    except Exception as e:
        print(f"Chat history error: {e}")
        return jsonify({'error': 'Failed to get chat history'}), 500

@chat_bp.route('/conversations', methods=['GET'])
@jwt_required()
def get_conversations():
    """Get user's conversations"""
    try:
        user_id = get_jwt_identity()
        
        conversations = find_documents(
            'conversations',
            {'user_id': ObjectId(user_id)},
            sort=[('last_activity', -1)]
        )
        
        formatted_conversations = []
        for conv in conversations:
            # Get last message
            last_message = find_document(
                'messages',
                {'conversation_id': conv['_id']},
                sort=[('timestamp', -1)]
            )
            
            formatted_conversations.append({
                'id': str(conv['_id']),
                'title': conv['title'],
                'created_at': conv['created_at'].isoformat(),
                'last_activity': conv['last_activity'].isoformat(),
                'last_message': last_message['message'][:100] + '...' if last_message and len(last_message['message']) > 100 else last_message['message'] if last_message else None
            })
        
        return jsonify({'conversations': formatted_conversations}), 200
        
    except Exception as e:
        print(f"Get conversations error: {e}")
        return jsonify({'error': 'Failed to get conversations'}), 500

@chat_bp.route('/conversations', methods=['POST'])
@jwt_required()
def create_conversation_endpoint():
    """Create a new conversation"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        title = data.get('title', 'New Conversation')
        
        conversation_id = create_conversation(user_id, title)
        
        return jsonify({
            'conversation_id': conversation_id,
            'title': title,
            'message': 'Conversation created successfully'
        }), 201
        
    except Exception as e:
        print(f"Create conversation error: {e}")
        return jsonify({'error': 'Failed to create conversation'}), 500

def create_conversation(user_id, title):
    """Helper function to create a conversation"""
    conversation_data = {
        'user_id': ObjectId(user_id),
        'title': title,
        'last_activity': datetime.utcnow()
    }
    
    conversation_id = insert_document('conversations', conversation_data)
    return str(conversation_id)

def save_message(conversation_id, user_id, message, sender, intent=None, confidence=None, metadata=None):
    """Helper function to save a message"""
    message_data = {
        'conversation_id': ObjectId(conversation_id),
        'user_id': ObjectId(user_id),
        'message': message,
        'sender': sender,
        'timestamp': datetime.utcnow(),
        'intent': intent,
        'confidence': confidence,
        'metadata': metadata
    }
    
    return insert_document('messages', message_data)
