from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId
from datetime import datetime
from models.database import find_documents, find_document
from models.user import User

academic_bp = Blueprint('academic', __name__)

@academic_bp.route('/courses', methods=['GET'])
@jwt_required()
def get_courses():
    """Get all courses"""
    try:
        courses = find_documents('courses', sort=[('course_code', 1)])
        
        formatted_courses = []
        for course in courses:
            formatted_courses.append({
                'id': str(course['_id']),
                'course_code': course['course_code'],
                'course_name': course['course_name'],
                'description': course.get('description', ''),
                'credits': course.get('credits', 0),
                'department': course.get('department', ''),
                'lecturer_id': str(course.get('lecturer_id', '')),
                'semester': course.get('semester', ''),
                'year': course.get('year', '')
            })
        
        return jsonify({'courses': formatted_courses}), 200
        
    except Exception as e:
        print(f"Get courses error: {e}")
        return jsonify({'error': 'Failed to get courses'}), 500

@academic_bp.route('/courses/<course_id>', methods=['GET'])
@jwt_required()
def get_course(course_id):
    """Get specific course details"""
    try:
        course = find_document('courses', {'_id': ObjectId(course_id)})
        
        if not course:
            return jsonify({'error': 'Course not found'}), 404
        
        # Get lecturer info
        lecturer = None
        if course.get('lecturer_id'):
            lecturer = find_document('lecturers', {'_id': course['lecturer_id']})
        
        formatted_course = {
            'id': str(course['_id']),
            'course_code': course['course_code'],
            'course_name': course['course_name'],
            'description': course.get('description', ''),
            'credits': course.get('credits', 0),
            'department': course.get('department', ''),
            'semester': course.get('semester', ''),
            'year': course.get('year', ''),
            'lecturer': {
                'id': str(lecturer['_id']),
                'name': lecturer['name'],
                'email': lecturer['email'],
                'office': lecturer.get('office', ''),
                'phone': lecturer.get('phone', '')
            } if lecturer else None
        }
        
        return jsonify({'course': formatted_course}), 200
        
    except Exception as e:
        print(f"Get course error: {e}")
        return jsonify({'error': 'Failed to get course'}), 500

@academic_bp.route('/schedule', methods=['GET'])
@jwt_required()
def get_schedule():
    """Get student's class schedule"""
    try:
        user_id = get_jwt_identity()
        user = User.find_by_id(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Get student's enrolled courses
        enrollments = find_documents('enrollments', {'student_id': user['student_id']})
        
        schedule = []
        for enrollment in enrollments:
            # Get course details
            course = find_document('courses', {'_id': enrollment['course_id']})
            if not course:
                continue
            
            # Get schedule for this course
            course_schedules = find_documents('schedules', {'course_id': enrollment['course_id']})
            
            for sched in course_schedules:
                schedule.append({
                    'course_code': course['course_code'],
                    'course_name': course['course_name'],
                    'day_of_week': sched['day_of_week'],
                    'start_time': sched['start_time'],
                    'end_time': sched['end_time'],
                    'room': sched.get('room', ''),
                    'building': sched.get('building', ''),
                    'lecturer': sched.get('lecturer', '')
                })
        
        # Sort by day and time
        days_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        schedule.sort(key=lambda x: (days_order.index(x['day_of_week']), x['start_time']))
        
        return jsonify({'schedule': schedule}), 200
        
    except Exception as e:
        print(f"Get schedule error: {e}")
        return jsonify({'error': 'Failed to get schedule'}), 500

@academic_bp.route('/assignments', methods=['GET'])
@jwt_required()
def get_assignments():
    """Get student's assignments"""
    try:
        user_id = get_jwt_identity()
        user = User.find_by_id(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        course_id = request.args.get('course_id')
        
        # Get student's enrolled courses
        enrollments = find_documents('enrollments', {'student_id': user['student_id']})
        enrolled_course_ids = [enrollment['course_id'] for enrollment in enrollments]
        
        # Build query
        query = {'course_id': {'$in': enrolled_course_ids}}
        if course_id:
            query['course_id'] = ObjectId(course_id)
        
        assignments = find_documents('assignments', query, sort=[('due_date', 1)])
        
        formatted_assignments = []
        for assignment in assignments:
            # Get course info
            course = find_document('courses', {'_id': assignment['course_id']})
            
            formatted_assignments.append({
                'id': str(assignment['_id']),
                'title': assignment['title'],
                'description': assignment.get('description', ''),
                'due_date': assignment['due_date'].isoformat(),
                'points': assignment.get('points', 0),
                'status': assignment.get('status', 'pending'),
                'course': {
                    'id': str(course['_id']),
                    'course_code': course['course_code'],
                    'course_name': course['course_name']
                } if course else None,
                'created_at': assignment['created_at'].isoformat()
            })
        
        return jsonify({'assignments': formatted_assignments}), 200
        
    except Exception as e:
        print(f"Get assignments error: {e}")
        return jsonify({'error': 'Failed to get assignments'}), 500

@academic_bp.route('/lecturers', methods=['GET'])
@jwt_required()
def get_lecturers():
    """Get all lecturers"""
    try:
        lecturers = find_documents('lecturers', sort=[('name', 1)])
        
        formatted_lecturers = []
        for lecturer in lecturers:
            # Get courses taught by this lecturer
            courses = find_documents('courses', {'lecturer_id': lecturer['_id']})
            
            formatted_lecturers.append({
                'id': str(lecturer['_id']),
                'name': lecturer['name'],
                'email': lecturer['email'],
                'phone': lecturer.get('phone', ''),
                'office': lecturer.get('office', ''),
                'department': lecturer.get('department', ''),
                'office_hours': lecturer.get('office_hours', ''),
                'courses': [
                    {
                        'id': str(course['_id']),
                        'course_code': course['course_code'],
                        'course_name': course['course_name']
                    } for course in courses
                ]
            })
        
        return jsonify({'lecturers': formatted_lecturers}), 200
        
    except Exception as e:
        print(f"Get lecturers error: {e}")
        return jsonify({'error': 'Failed to get lecturers'}), 500

@academic_bp.route('/exams', methods=['GET'])
@jwt_required()
def get_exams():
    """Get student's exam schedule"""
    try:
        user_id = get_jwt_identity()
        user = User.find_by_id(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Get student's enrolled courses
        enrollments = find_documents('enrollments', {'student_id': user['student_id']})
        enrolled_course_ids = [enrollment['course_id'] for enrollment in enrollments]
        
        # Get exams for enrolled courses
        exams = find_documents(
            'exams', 
            {'course_id': {'$in': enrolled_course_ids}},
            sort=[('exam_date', 1)]
        )
        
        formatted_exams = []
        for exam in exams:
            # Get course info
            course = find_document('courses', {'_id': exam['course_id']})
            
            formatted_exams.append({
                'id': str(exam['_id']),
                'exam_type': exam.get('exam_type', 'Final'),
                'exam_date': exam['exam_date'].isoformat(),
                'start_time': exam.get('start_time', ''),
                'end_time': exam.get('end_time', ''),
                'room': exam.get('room', ''),
                'building': exam.get('building', ''),
                'instructions': exam.get('instructions', ''),
                'course': {
                    'id': str(course['_id']),
                    'course_code': course['course_code'],
                    'course_name': course['course_name']
                } if course else None
            })
        
        return jsonify({'exams': formatted_exams}), 200
        
    except Exception as e:
        print(f"Get exams error: {e}")
        return jsonify({'error': 'Failed to get exams'}), 500
