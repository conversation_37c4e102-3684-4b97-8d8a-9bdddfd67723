from pymongo import MongoClient
from datetime import datetime
import os

# Global database connection
db = None
client = None

def init_db(mongodb_uri):
    """Initialize database connection"""
    global db, client
    try:
        client = MongoClient(mongodb_uri)
        db = client.get_default_database()
        
        # Test connection
        client.admin.command('ping')
        print("Successfully connected to MongoDB")
        
        # Create indexes for better performance
        create_indexes()
        
        return db
    except Exception as e:
        print(f"Failed to connect to MongoDB: {e}")
        raise

def create_indexes():
    """Create database indexes for better performance"""
    try:
        # User indexes
        db.users.create_index("email", unique=True)
        db.users.create_index("student_id", unique=True)
        
        # Conversation indexes
        db.conversations.create_index([("user_id", 1), ("created_at", -1)])
        
        # Message indexes
        db.messages.create_index([("conversation_id", 1), ("timestamp", 1)])
        db.messages.create_index([("user_id", 1), ("timestamp", -1)])
        
        # Academic data indexes
        db.courses.create_index("course_code", unique=True)
        db.assignments.create_index([("course_id", 1), ("due_date", 1)])
        db.schedules.create_index([("student_id", 1), ("day_of_week", 1)])
        
        print("Database indexes created successfully")
    except Exception as e:
        print(f"Error creating indexes: {e}")

def get_db():
    """Get database instance"""
    return db

def close_db():
    """Close database connection"""
    global client
    if client:
        client.close()
        print("Database connection closed")

# Collection helper functions
def get_collection(name):
    """Get a specific collection"""
    return db[name]

def insert_document(collection_name, document):
    """Insert a document into a collection"""
    document['created_at'] = datetime.utcnow()
    document['updated_at'] = datetime.utcnow()
    result = db[collection_name].insert_one(document)
    return result.inserted_id

def update_document(collection_name, filter_dict, update_dict):
    """Update a document in a collection"""
    update_dict['updated_at'] = datetime.utcnow()
    result = db[collection_name].update_one(filter_dict, {'$set': update_dict})
    return result.modified_count

def find_document(collection_name, filter_dict):
    """Find a single document in a collection"""
    return db[collection_name].find_one(filter_dict)

def find_documents(collection_name, filter_dict=None, sort=None, limit=None):
    """Find multiple documents in a collection"""
    cursor = db[collection_name].find(filter_dict or {})
    
    if sort:
        cursor = cursor.sort(sort)
    
    if limit:
        cursor = cursor.limit(limit)
    
    return list(cursor)

def delete_document(collection_name, filter_dict):
    """Delete a document from a collection"""
    result = db[collection_name].delete_one(filter_dict)
    return result.deleted_count

def count_documents(collection_name, filter_dict=None):
    """Count documents in a collection"""
    return db[collection_name].count_documents(filter_dict or {})
