from bson import ObjectId
from datetime import datetime
import bcrypt
from .database import get_db, insert_document, find_document, update_document

class User:
    def __init__(self, email, name, student_id, program, year, password_hash=None):
        self.email = email
        self.name = name
        self.student_id = student_id
        self.program = program
        self.year = year
        self.password_hash = password_hash
        self.created_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        self.is_active = True
        self.last_login = None

    @staticmethod
    def hash_password(password):
        """Hash a password using bcrypt"""
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

    @staticmethod
    def verify_password(password, password_hash):
        """Verify a password against its hash"""
        return bcrypt.checkpw(password.encode('utf-8'), password_hash)

    def to_dict(self):
        """Convert user object to dictionary"""
        return {
            'email': self.email,
            'name': self.name,
            'student_id': self.student_id,
            'program': self.program,
            'year': self.year,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'is_active': self.is_active,
            'last_login': self.last_login
        }

    def save(self):
        """Save user to database"""
        user_data = self.to_dict()
        user_data['password_hash'] = self.password_hash
        return insert_document('users', user_data)

    @staticmethod
    def find_by_email(email):
        """Find user by email"""
        return find_document('users', {'email': email})

    @staticmethod
    def find_by_student_id(student_id):
        """Find user by student ID"""
        return find_document('users', {'student_id': student_id})

    @staticmethod
    def find_by_id(user_id):
        """Find user by ID"""
        if isinstance(user_id, str):
            user_id = ObjectId(user_id)
        return find_document('users', {'_id': user_id})

    @staticmethod
    def create_user(email, name, student_id, program, year, password):
        """Create a new user"""
        # Check if user already exists
        if User.find_by_email(email):
            raise ValueError("User with this email already exists")
        
        if User.find_by_student_id(student_id):
            raise ValueError("User with this student ID already exists")

        # Hash password
        password_hash = User.hash_password(password)

        # Create user instance
        user = User(email, name, student_id, program, year, password_hash)
        
        # Save to database
        user_id = user.save()
        
        # Return user data without password hash
        user_data = user.to_dict()
        user_data['_id'] = user_id
        
        return user_data

    @staticmethod
    def authenticate(email, password):
        """Authenticate user with email and password"""
        user = User.find_by_email(email)
        
        if not user:
            return None
        
        if not User.verify_password(password, user['password_hash']):
            return None
        
        # Update last login
        update_document('users', 
                       {'_id': user['_id']}, 
                       {'last_login': datetime.utcnow()})
        
        # Remove password hash from returned data
        user.pop('password_hash', None)
        
        return user

    @staticmethod
    def update_profile(user_id, update_data):
        """Update user profile"""
        if isinstance(user_id, str):
            user_id = ObjectId(user_id)
        
        # Remove sensitive fields that shouldn't be updated directly
        update_data.pop('password_hash', None)
        update_data.pop('_id', None)
        update_data.pop('created_at', None)
        
        return update_document('users', {'_id': user_id}, update_data)

    @staticmethod
    def change_password(user_id, old_password, new_password):
        """Change user password"""
        if isinstance(user_id, str):
            user_id = ObjectId(user_id)
        
        user = User.find_by_id(user_id)
        if not user:
            return False
        
        # Verify old password
        if not User.verify_password(old_password, user['password_hash']):
            return False
        
        # Hash new password
        new_password_hash = User.hash_password(new_password)
        
        # Update password
        return update_document('users', 
                             {'_id': user_id}, 
                             {'password_hash': new_password_hash}) > 0
