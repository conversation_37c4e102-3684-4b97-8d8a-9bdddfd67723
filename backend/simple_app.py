#!/usr/bin/env python3
"""
Simplified Flask backend for the Academic Chatbot
This version works without MongoDB for immediate testing
"""

import os
import json
from datetime import datetime, timedelta
from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_jwt_extended import J<PERSON><PERSON>ana<PERSON>, create_access_token, jwt_required, get_jwt_identity
import bcrypt

# Create Flask app
app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = 'dev-secret-key-change-in-production'
app.config['JWT_SECRET_KEY'] = 'jwt-secret-change-in-production'
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)

# Initialize extensions
CORS(app, origins=['http://localhost:5174', 'http://localhost:5173', 'http://localhost:3000'])
jwt = JWTManager(app)

# In-memory storage (for demo purposes)
users_db = {}
conversations_db = {}
messages_db = []

# Sample academic data
sample_courses = [
    {
        'id': '1',
        'course_code': 'CS101',
        'course_name': 'Introduction to Computer Science',
        'description': 'Fundamental concepts of computer science including programming basics, algorithms, and data structures.',
        'credits': 3,
        'department': 'Computer Science',
        'lecturer': 'Dr. Sarah Johnson'
    },
    {
        'id': '2',
        'course_code': 'MATH201',
        'course_name': 'Calculus I',
        'description': 'Limits, derivatives, applications of derivatives, and introduction to integrals.',
        'credits': 4,
        'department': 'Mathematics',
        'lecturer': 'Prof. Michael Chen'
    },
    {
        'id': '3',
        'course_code': 'ENG101',
        'course_name': 'English Composition',
        'description': 'Academic writing, research methods, and critical thinking skills.',
        'credits': 3,
        'department': 'English',
        'lecturer': 'Prof. David Wilson'
    }
]

sample_assignments = [
    {
        'id': '1',
        'title': 'Programming Assignment 1: Hello World',
        'course': 'CS101',
        'due_date': '2024-02-15',
        'description': 'Write your first program that prints "Hello, World!"'
    },
    {
        'id': '2',
        'title': 'Calculus Problem Set 1',
        'course': 'MATH201',
        'due_date': '2024-02-20',
        'description': 'Solve problems involving limits and continuity'
    }
]

# Utility functions
def hash_password(password):
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

def verify_password(password, password_hash):
    return bcrypt.checkpw(password.encode('utf-8'), password_hash)

def generate_bot_response(message, user_data):
    """Simple rule-based chatbot responses"""
    message_lower = message.lower()
    
    if any(word in message_lower for word in ['hello', 'hi', 'hey']):
        return f"Hello {user_data['name']}! I'm your academic assistant. I can help you with course information, schedules, assignments, and lecturer contacts. What would you like to know?"
    
    elif any(word in message_lower for word in ['course', 'class', 'subject']):
        courses_text = "Here are your available courses:\n\n"
        for course in sample_courses:
            courses_text += f"• **{course['course_code']}** - {course['course_name']}\n"
            courses_text += f"  Instructor: {course['lecturer']}\n"
            courses_text += f"  Credits: {course['credits']}\n\n"
        return courses_text
    
    elif any(word in message_lower for word in ['assignment', 'homework', 'due']):
        assignments_text = "Here are your upcoming assignments:\n\n"
        for assignment in sample_assignments:
            assignments_text += f"• **{assignment['title']}**\n"
            assignments_text += f"  Course: {assignment['course']}\n"
            assignments_text += f"  Due: {assignment['due_date']}\n"
            assignments_text += f"  Description: {assignment['description']}\n\n"
        return assignments_text
    
    elif any(word in message_lower for word in ['schedule', 'timetable', 'time']):
        return "Your class schedule:\n\n• **CS101** - Mon/Wed/Fri 9:00-10:00 AM\n• **MATH201** - Tue/Thu 11:00-12:30 PM\n• **ENG101** - Mon/Wed 2:00-3:00 PM"
    
    elif any(word in message_lower for word in ['lecturer', 'professor', 'instructor', 'contact']):
        return "Lecturer contact information:\n\n• **Dr. Sarah Johnson** (CS101)\n  Email: <EMAIL>\n  Office: CS Building, Room 301\n\n• **Prof. Michael Chen** (MATH201)\n  Email: <EMAIL>\n  Office: Math Building, Room 205"
    
    else:
        return "I can help you with:\n• Course information\n• Assignment deadlines\n• Class schedules\n• Lecturer contact details\n\nWhat would you like to know about?"

# Routes
@app.route('/api/health')
def health_check():
    return jsonify({
        'status': 'healthy',
        'message': 'Academic Chatbot API is running',
        'timestamp': datetime.utcnow().isoformat()
    })

@app.route('/api/auth/register', methods=['POST'])
def register():
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['email', 'password', 'name', 'student_id', 'program', 'year']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400
        
        email = data['email'].lower().strip()
        
        # Check if user already exists
        if email in users_db:
            return jsonify({'error': 'User with this email already exists'}), 400
        
        # Create user
        user_id = f"user_{len(users_db) + 1}"
        password_hash = hash_password(data['password'])
        
        user_data = {
            'id': user_id,
            'email': email,
            'name': data['name'].strip(),
            'student_id': data['student_id'].strip(),
            'program': data['program'].strip(),
            'year': int(data['year']),
            'created_at': datetime.utcnow().isoformat()
        }
        
        users_db[email] = {
            **user_data,
            'password_hash': password_hash
        }
        
        # Create access token
        access_token = create_access_token(identity=user_id)
        
        return jsonify({
            'message': 'User registered successfully',
            'user': user_data,
            'token': access_token
        }), 201
        
    except Exception as e:
        print(f"Registration error: {e}")
        return jsonify({'error': 'Registration failed'}), 500

@app.route('/api/auth/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        
        email = data.get('email', '').lower().strip()
        password = data.get('password', '')
        
        if not email or not password:
            return jsonify({'error': 'Email and password are required'}), 400
        
        # Find user
        user = users_db.get(email)
        if not user or not verify_password(password, user['password_hash']):
            return jsonify({'error': 'Invalid email or password'}), 401
        
        # Create access token
        access_token = create_access_token(identity=user['id'])
        
        # Remove password hash from response
        user_data = {k: v for k, v in user.items() if k != 'password_hash'}
        
        return jsonify({
            'message': 'Login successful',
            'user': user_data,
            'token': access_token
        }), 200
        
    except Exception as e:
        print(f"Login error: {e}")
        return jsonify({'error': 'Login failed'}), 500

@app.route('/api/auth/profile', methods=['GET'])
@jwt_required()
def get_profile():
    try:
        user_id = get_jwt_identity()
        
        # Find user by ID
        user = None
        for email, user_data in users_db.items():
            if user_data['id'] == user_id:
                user = {k: v for k, v in user_data.items() if k != 'password_hash'}
                break
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        return jsonify({'user': user}), 200
        
    except Exception as e:
        print(f"Profile error: {e}")
        return jsonify({'error': 'Failed to get profile'}), 500

@app.route('/api/chat', methods=['POST'])
@jwt_required()
def send_message():
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        message = data.get('message', '').strip()
        if not message:
            return jsonify({'error': 'Message is required'}), 400
        
        # Find user
        user = None
        for email, user_data in users_db.items():
            if user_data['id'] == user_id:
                user = user_data
                break
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Generate bot response
        bot_response = generate_bot_response(message, user)
        
        # Store messages (simplified)
        conversation_id = f"conv_{user_id}"
        messages_db.append({
            'conversation_id': conversation_id,
            'user_id': user_id,
            'message': message,
            'sender': 'user',
            'timestamp': datetime.utcnow().isoformat()
        })
        
        messages_db.append({
            'conversation_id': conversation_id,
            'user_id': user_id,
            'message': bot_response,
            'sender': 'bot',
            'timestamp': datetime.utcnow().isoformat()
        })
        
        return jsonify({
            'response': bot_response,
            'conversation_id': conversation_id,
            'intent': 'academic_query',
            'confidence': 0.85
        }), 200
        
    except Exception as e:
        print(f"Chat error: {e}")
        return jsonify({'error': 'Failed to process message'}), 500

@app.route('/api/chat/history', methods=['GET'])
@jwt_required()
def get_chat_history():
    try:
        user_id = get_jwt_identity()
        conversation_id = request.args.get('conversation_id', f"conv_{user_id}")
        
        # Get messages for this conversation
        user_messages = [
            {
                'id': str(i),
                'text': msg['message'],
                'sender': msg['sender'],
                'timestamp': msg['timestamp']
            }
            for i, msg in enumerate(messages_db)
            if msg['conversation_id'] == conversation_id
        ]
        
        return jsonify({
            'messages': user_messages,
            'conversation': {
                'id': conversation_id,
                'title': 'Academic Chat',
                'created_at': datetime.utcnow().isoformat()
            }
        }), 200
        
    except Exception as e:
        print(f"Chat history error: {e}")
        return jsonify({'error': 'Failed to get chat history'}), 500

if __name__ == '__main__':
    print("🚀 Starting Academic Chatbot Backend...")
    print("📱 Frontend should be at: http://localhost:5174")
    print("🔧 Backend API at: http://localhost:5000")
    print("📊 Health check: http://localhost:5000/api/health")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
