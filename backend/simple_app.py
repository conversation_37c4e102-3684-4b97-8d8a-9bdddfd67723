#!/usr/bin/env python3
"""
Simplified Flask backend for the Academic Chatbot
This version works without MongoDB for immediate testing
"""

import os
import json
import requests
from datetime import datetime, timedelta
from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_jwt_extended import J<PERSON><PERSON>ana<PERSON>, create_access_token, jwt_required, get_jwt_identity
import bcrypt
import pymongo
from bson import ObjectId

# Create Flask app
app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = 'dev-secret-key-change-in-production'
app.config['JWT_SECRET_KEY'] = 'jwt-secret-change-in-production'
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)

# Initialize extensions
CORS(app, origins=['http://localhost:5174', 'http://localhost:5173', 'http://localhost:3000'])
jwt = JWTManager(app)

# MongoDB connection
MONGODB_URI = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/chatbot_db')
RASA_URL = os.getenv('RASA_URL', 'http://localhost:5005')

def get_db():
    """Get MongoDB database connection"""
    try:
        client = pymongo.MongoClient(MONGODB_URI)
        db = client.get_default_database()
        return db
    except Exception as e:
        print(f"Database connection error: {e}")
        return None

# In-memory storage (fallback when MongoDB is not available)
users_db = {}
conversations_db = {}
messages_db = []

def save_to_db(collection_name, document):
    """Save document to MongoDB or fallback to in-memory storage"""
    db = get_db()
    if db is not None:
        try:
            result = db[collection_name].insert_one(document)
            return str(result.inserted_id)
        except Exception as e:
            print(f"Database save error: {e}")

    # Fallback to in-memory storage
    doc_id = f"{collection_name}_{len(globals().get(f'{collection_name}_db', {})) + 1}"
    document['_id'] = doc_id
    if collection_name == 'conversations':
        conversations_db[doc_id] = document
    elif collection_name == 'messages':
        messages_db.append(document)
    return doc_id

def find_from_db(collection_name, filter_dict=None, sort=None):
    """Find documents from MongoDB or fallback to in-memory storage"""
    db = get_db()
    if db is not None:
        try:
            cursor = db[collection_name].find(filter_dict or {})
            if sort:
                cursor = cursor.sort(sort)
            return list(cursor)
        except Exception as e:
            print(f"Database find error: {e}")

    # Fallback to in-memory storage
    if collection_name == 'conversations':
        return list(conversations_db.values())
    elif collection_name == 'messages':
        return messages_db
    return []

# Sample academic data
sample_courses = [
    {
        'id': '1',
        'course_code': 'CS101',
        'course_name': 'Introduction to Computer Science',
        'description': 'Fundamental concepts of computer science including programming basics, algorithms, and data structures.',
        'credits': 3,
        'department': 'Computer Science',
        'lecturer': 'Dr. Sarah Johnson'
    },
    {
        'id': '2',
        'course_code': 'MATH201',
        'course_name': 'Calculus I',
        'description': 'Limits, derivatives, applications of derivatives, and introduction to integrals.',
        'credits': 4,
        'department': 'Mathematics',
        'lecturer': 'Prof. Michael Chen'
    },
    {
        'id': '3',
        'course_code': 'ENG101',
        'course_name': 'English Composition',
        'description': 'Academic writing, research methods, and critical thinking skills.',
        'credits': 3,
        'department': 'English',
        'lecturer': 'Prof. David Wilson'
    }
]

sample_assignments = [
    {
        'id': '1',
        'title': 'Programming Assignment 1: Hello World',
        'course': 'CS101',
        'due_date': '2024-02-15',
        'description': 'Write your first program that prints "Hello, World!"'
    },
    {
        'id': '2',
        'title': 'Calculus Problem Set 1',
        'course': 'MATH201',
        'due_date': '2024-02-20',
        'description': 'Solve problems involving limits and continuity'
    }
]

# Utility functions
def hash_password(password):
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

def verify_password(password, password_hash):
    return bcrypt.checkpw(password.encode('utf-8'), password_hash)

def send_to_rasa(message, sender_id):
    """Send message to Rasa and get response"""
    try:
        payload = {
            "sender": sender_id,
            "message": message
        }

        response = requests.post(
            f"{RASA_URL}/webhooks/rest/webhook",
            json=payload,
            timeout=10
        )

        if response.status_code == 200:
            rasa_responses = response.json()
            if rasa_responses:
                return rasa_responses[0].get('text', 'I apologize, but I encountered an error.')
            else:
                return "I'm not sure how to respond to that. Can you try rephrasing?"
        else:
            return None

    except Exception as e:
        print(f"Rasa connection error: {e}")
        return None

def generate_bot_response(message, user_data):
    """Generate bot response using Rasa or fallback to simple responses"""
    # Try Rasa first
    rasa_response = send_to_rasa(message, user_data.get('id', 'user'))

    if rasa_response:
        return rasa_response

    # Fallback to simple rule-based responses
    message_lower = message.lower()

    if any(word in message_lower for word in ['hello', 'hi', 'hey']):
        return f"Hello {user_data['name']}! I'm your academic assistant. I can help you with course information, schedules, assignments, and lecturer contacts. What would you like to know?"

    elif any(word in message_lower for word in ['course', 'class', 'subject']):
        courses_text = "Here are your available courses:\n\n"
        for course in sample_courses:
            courses_text += f"• **{course['course_code']}** - {course['course_name']}\n"
            courses_text += f"  Instructor: {course['lecturer']}\n"
            courses_text += f"  Credits: {course['credits']}\n\n"
        return courses_text

    elif any(word in message_lower for word in ['assignment', 'homework', 'due']):
        assignments_text = "Here are your upcoming assignments:\n\n"
        for assignment in sample_assignments:
            assignments_text += f"• **{assignment['title']}**\n"
            assignments_text += f"  Course: {assignment['course']}\n"
            assignments_text += f"  Due: {assignment['due_date']}\n"
            assignments_text += f"  Description: {assignment['description']}\n\n"
        return assignments_text

    elif any(word in message_lower for word in ['schedule', 'timetable', 'time']):
        return "Your class schedule:\n\n• **CS101** - Mon/Wed/Fri 9:00-10:00 AM\n• **MATH201** - Tue/Thu 11:00-12:30 PM\n• **ENG101** - Mon/Wed 2:00-3:00 PM"

    elif any(word in message_lower for word in ['lecturer', 'professor', 'instructor', 'contact']):
        return "Lecturer contact information:\n\n• **Dr. Sarah Johnson** (CS101)\n  Email: <EMAIL>\n  Office: CS Building, Room 301\n\n• **Prof. Michael Chen** (MATH201)\n  Email: <EMAIL>\n  Office: Math Building, Room 205"

    # 🎯 ADD YOUR CUSTOM RESPONSES HERE:
    elif any(word in message_lower for word in ['library', 'books', 'study']):
        return f"📚 **Library Information for {user_data['name']}:**\n\n• **Main Library**: Open 24/7 during exams\n• **Study Rooms**: Book online at library.university.edu\n• **Digital Resources**: Access via student portal\n• **Research Help**: <EMAIL>\n\nNeed help finding specific resources?"

    elif any(word in message_lower for word in ['exam', 'test', 'quiz', 'midterm', 'final']):
        return f"📝 **Exam Information:**\n\n• **Midterm Exams**: March 15-22, 2024\n• **Final Exams**: May 10-17, 2024\n• **Exam Schedule**: Check student portal\n• **Study Tips**: Visit academic success center\n\nGood luck with your studies, {user_data['name']}!"

    elif any(word in message_lower for word in ['cafeteria', 'food', 'dining', 'meal']):
        return "🍽️ **Campus Dining:**\n\n• **Main Cafeteria**: 7 AM - 9 PM\n• **Student Union**: 11 AM - 8 PM\n• **Coffee Shop**: 6 AM - 10 PM\n• **Meal Plans**: Available at dining services\n\nToday's special: Grilled chicken with rice!"

    elif any(word in message_lower for word in ['parking', 'car', 'transportation']):
        return "🚗 **Campus Transportation:**\n\n• **Student Parking**: Lots A, B, C (permit required)\n• **Visitor Parking**: Lot D (hourly rates)\n• **Campus Shuttle**: Every 15 minutes\n• **Bike Racks**: Available near all buildings\n\nParking permits: $50/semester"

    elif any(word in message_lower for word in ['help', 'support', 'counseling']):
        return f"🤝 **Student Support Services:**\n\n• **Academic Advising**: <EMAIL>\n• **Counseling Center**: 24/7 crisis support\n• **Tutoring Center**: Free peer tutoring\n• **Career Services**: Resume help & job placement\n\nWe're here to help you succeed, {user_data['name']}!"

    elif any(word in message_lower for word in ['weather', 'campus', 'events']):
        return "🌤️ **Campus Updates:**\n\n• **Today's Weather**: Sunny, 72°F\n• **Upcoming Events**: Spring Festival (April 15)\n• **Campus News**: Check university website\n• **Emergency Alerts**: Text alerts to student phones\n\nStay connected with campus life!"

    else:
        return f"Hello {user_data['name']}! 🎓 I can help you with:\n\n• 📚 Course information & schedules\n• 📝 Assignment deadlines & exams\n• 👨‍🏫 Lecturer contact details\n• 📖 Library & study resources\n• 🍽️ Campus dining & services\n• 🚗 Parking & transportation\n• 🤝 Student support services\n\nWhat would you like to know about?"

# Routes
@app.route('/api/health')
def health_check():
    return jsonify({
        'status': 'healthy',
        'message': 'Academic Chatbot API is running',
        'timestamp': datetime.utcnow().isoformat()
    })

@app.route('/api/auth/register', methods=['POST'])
def register():
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['email', 'password', 'name', 'student_id', 'program', 'year']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400
        
        email = data['email'].lower().strip()

        # Check if user already exists in MongoDB or in-memory
        db = get_db()
        existing_user = None

        if db is not None:
            try:
                existing_user = db.users.find_one({'email': email})
            except Exception as e:
                print(f"Database check error: {e}")

        # Also check in-memory storage
        if existing_user or email in users_db:
            return jsonify({'error': 'User with this email already exists'}), 400

        # Create user
        password_hash = hash_password(data['password'])

        user_data = {
            'email': email,
            'name': data['name'].strip(),
            'student_id': data['student_id'].strip(),
            'program': data['program'].strip(),
            'year': int(data['year']),
            'created_at': datetime.utcnow().isoformat(),
            'password_hash': password_hash
        }

        # Save to MongoDB Atlas
        user_id = None
        if db is not None:
            try:
                result = db.users.insert_one(user_data)
                user_id = str(result.inserted_id)
                print(f"✅ User saved to MongoDB Atlas: {email}")
            except Exception as e:
                print(f"❌ MongoDB save error: {e}")

        # Fallback to in-memory storage
        if not user_id:
            user_id = f"user_{len(users_db) + 1}"
            user_data['_id'] = user_id
            users_db[email] = user_data
            print(f"⚠️ User saved to in-memory storage: {email}")

        # Remove password hash from response and convert ObjectId to string
        response_user_data = {}
        for k, v in user_data.items():
            if k != 'password_hash':
                if k == '_id':
                    response_user_data['id'] = str(v)
                else:
                    response_user_data[k] = v

        if 'id' not in response_user_data:
            response_user_data['id'] = user_id
        
        # Create access token
        access_token = create_access_token(identity=user_id)
        
        return jsonify({
            'message': 'User registered successfully',
            'user': response_user_data,
            'token': access_token
        }), 201
        
    except Exception as e:
        print(f"Registration error: {e}")
        return jsonify({'error': 'Registration failed'}), 500

@app.route('/api/auth/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        
        email = data.get('email', '').lower().strip()
        password = data.get('password', '')
        
        if not email or not password:
            return jsonify({'error': 'Email and password are required'}), 400
        
        # Find user in MongoDB first, then fallback to in-memory
        user = None
        db = get_db()

        if db is not None:
            try:
                user = db.users.find_one({'email': email})
                if user:
                    print(f"✅ User found in MongoDB Atlas: {email}")
            except Exception as e:
                print(f"❌ MongoDB find error: {e}")

        # Fallback to in-memory storage
        if not user:
            user = users_db.get(email)
            if user:
                print(f"⚠️ User found in in-memory storage: {email}")

        if not user or not verify_password(password, user['password_hash']):
            return jsonify({'error': 'Invalid email or password'}), 401

        # Create access token
        user_id = str(user.get('_id', user.get('id')))
        access_token = create_access_token(identity=user_id)
        
        # Remove password hash from response and convert ObjectId to string
        user_data = {}
        for k, v in user.items():
            if k != 'password_hash':
                if k == '_id':
                    user_data['id'] = str(v)
                else:
                    user_data[k] = v

        if 'id' not in user_data:
            user_data['id'] = user_id

        return jsonify({
            'message': 'Login successful',
            'user': user_data,
            'token': access_token
        }), 200
        
    except Exception as e:
        print(f"Login error: {e}")
        return jsonify({'error': 'Login failed'}), 500

@app.route('/api/auth/profile', methods=['GET'])
@jwt_required()
def get_profile():
    try:
        user_id = get_jwt_identity()
        
        # Find user by ID
        user = None
        for email, user_data in users_db.items():
            if user_data['id'] == user_id:
                user = {k: v for k, v in user_data.items() if k != 'password_hash'}
                break
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        return jsonify({'user': user}), 200
        
    except Exception as e:
        print(f"Profile error: {e}")
        return jsonify({'error': 'Failed to get profile'}), 500

@app.route('/api/auth/logout', methods=['POST'])
@jwt_required()
def logout():
    """Logout user (client-side token removal)"""
    return jsonify({'message': 'Logout successful'}), 200

@app.route('/api/chat', methods=['POST'])
@jwt_required()
def send_message():
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        message = data.get('message', '').strip()
        conversation_id = data.get('conversation_id')

        if not message:
            return jsonify({'error': 'Message is required'}), 400
        
        # Find user in MongoDB first, then fallback to in-memory
        user = None
        db = get_db()

        if db is not None:
            try:
                # Try to find by ObjectId first
                try:
                    user = db.users.find_one({'_id': ObjectId(user_id)})
                except:
                    # If not ObjectId, try as string
                    user = db.users.find_one({'_id': user_id})

                if user:
                    print(f"✅ User found in MongoDB Atlas for chat: {user.get('email')}")
            except Exception as e:
                print(f"❌ MongoDB user lookup error: {e}")

        # Fallback to in-memory storage
        if not user:
            for email, user_data in users_db.items():
                if user_data.get('id') == user_id or user_data.get('_id') == user_id:
                    user = user_data
                    print(f"⚠️ User found in in-memory storage for chat: {email}")
                    break

        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Generate bot response
        bot_response = generate_bot_response(message, user)
        
        # Handle conversation ID
        if not conversation_id:
            conversation_id = f"conv_{user_id}"
            # Create conversation if it doesn't exist
            conversation_data = {
                'user_id': user_id,
                'title': 'Academic Chat',
                'created_at': datetime.utcnow().isoformat(),
                'last_activity': datetime.utcnow().isoformat()
            }
            save_to_db('conversations', conversation_data)

        # Store messages
        user_message_data = {
            'conversation_id': conversation_id,
            'user_id': user_id,
            'message': message,
            'sender': 'user',
            'timestamp': datetime.utcnow().isoformat()
        }
        save_to_db('messages', user_message_data)

        bot_message_data = {
            'conversation_id': conversation_id,
            'user_id': user_id,
            'message': bot_response,
            'sender': 'bot',
            'timestamp': datetime.utcnow().isoformat()
        }
        save_to_db('messages', bot_message_data)
        
        return jsonify({
            'response': bot_response,
            'conversation_id': conversation_id,
            'intent': 'academic_query',
            'confidence': 0.85
        }), 200
        
    except Exception as e:
        print(f"Chat error: {e}")
        return jsonify({'error': 'Failed to process message'}), 500

@app.route('/api/chat/history', methods=['GET'])
@jwt_required()
def get_chat_history():
    try:
        user_id = get_jwt_identity()
        conversation_id = request.args.get('conversation_id', f"conv_{user_id}")
        
        # Get messages for this conversation
        user_messages = [
            {
                'id': str(i),
                'text': msg['message'],
                'sender': msg['sender'],
                'timestamp': msg['timestamp']
            }
            for i, msg in enumerate(messages_db)
            if msg['conversation_id'] == conversation_id
        ]
        
        return jsonify({
            'messages': user_messages,
            'conversation': {
                'id': conversation_id,
                'title': 'Academic Chat',
                'created_at': datetime.utcnow().isoformat()
            }
        }), 200
        
    except Exception as e:
        print(f"Chat history error: {e}")
        return jsonify({'error': 'Failed to get chat history'}), 500

@app.route('/api/chat/conversations', methods=['GET'])
@jwt_required()
def get_conversations():
    """Get user's conversations"""
    try:
        user_id = get_jwt_identity()

        # Get conversations from database or in-memory storage
        conversations = find_from_db('conversations', {'user_id': user_id}, [('last_activity', -1)])

        formatted_conversations = []
        for conv in conversations:
            # Get last message for preview
            messages = find_from_db('messages', {'conversation_id': conv.get('_id', conv.get('id'))})
            last_message = messages[-1] if messages else None

            formatted_conversations.append({
                'id': str(conv.get('_id', conv.get('id'))),
                'title': conv.get('title', 'New Conversation'),
                'created_at': conv.get('created_at', datetime.utcnow().isoformat()),
                'last_activity': conv.get('last_activity', datetime.utcnow().isoformat()),
                'last_message': last_message['message'][:100] if last_message else None
            })

        return jsonify({'conversations': formatted_conversations}), 200

    except Exception as e:
        print(f"Get conversations error: {e}")
        return jsonify({'error': 'Failed to get conversations'}), 500

@app.route('/api/chat/conversations', methods=['POST'])
@jwt_required()
def create_conversation():
    """Create a new conversation"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        title = data.get('title', 'New Conversation')

        conversation_data = {
            'user_id': user_id,
            'title': title,
            'created_at': datetime.utcnow().isoformat(),
            'last_activity': datetime.utcnow().isoformat()
        }

        conversation_id = save_to_db('conversations', conversation_data)

        return jsonify({
            'conversation_id': conversation_id,
            'title': title,
            'message': 'Conversation created successfully'
        }), 201

    except Exception as e:
        print(f"Create conversation error: {e}")
        return jsonify({'error': 'Failed to create conversation'}), 500

@app.route('/api/chat/conversations/<conversation_id>', methods=['DELETE'])
@jwt_required()
def delete_conversation(conversation_id):
    """Delete a conversation"""
    try:
        user_id = get_jwt_identity()

        # For now, just return success (actual deletion would require more complex logic)
        return jsonify({'message': 'Conversation deleted successfully'}), 200

    except Exception as e:
        print(f"Delete conversation error: {e}")
        return jsonify({'error': 'Failed to delete conversation'}), 500

if __name__ == '__main__':
    print("🚀 Starting Academic Chatbot Backend...")
    print("📱 Frontend should be at: http://localhost:5174")
    print("🔧 Backend API at: http://localhost:5000")
    print("📊 Health check: http://localhost:5000/api/health")
    
    app.run(host='0.0.0.0', port=5001, debug=True)
