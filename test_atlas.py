#!/usr/bin/env python3
"""
Quick MongoDB Atlas Connection Test
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv('backend/.env')

def test_atlas_connection():
    """Test MongoDB Atlas connection"""
    try:
        import pymongo
        
        # Get connection string from environment
        mongodb_uri = os.getenv('MONGODB_URI')
        
        if not mongodb_uri:
            print("❌ No MONGODB_URI found in backend/.env")
            print("💡 Run: python3 configure_atlas.py")
            return False
        
        print("🔍 Testing MongoDB Atlas connection...")
        print(f"🌐 URI: {mongodb_uri.replace(mongodb_uri.split('@')[0].split('//')[1], '***:***')}")
        
        # Test connection
        client = pymongo.MongoClient(mongodb_uri, serverSelectionTimeoutMS=10000)
        
        # Ping the server
        client.admin.command('ping')
        print("✅ Connection successful!")
        
        # Get database info
        db_name = mongodb_uri.split('/')[-1].split('?')[0]
        db = client[db_name]
        
        print(f"📊 Database: {db_name}")
        
        # List collections
        collections = db.list_collection_names()
        print(f"📁 Collections: {collections if collections else 'None (empty database)'}")
        
        # Test write operation
        test_collection = db.test_connection
        result = test_collection.insert_one({"test": "connection", "timestamp": "2024"})
        print(f"✅ Write test successful: {result.inserted_id}")
        
        # Clean up test document
        test_collection.delete_one({"_id": result.inserted_id})
        print("🧹 Cleaned up test document")
        
        client.close()
        return True
        
    except ImportError:
        print("❌ PyMongo not installed")
        print("💡 Install with: pip install pymongo python-dotenv")
        return False
    except pymongo.errors.ServerSelectionTimeoutError:
        print("❌ Connection timeout")
        print("🔧 Troubleshooting:")
        print("   - Check your internet connection")
        print("   - Verify the connection string is correct")
        print("   - Ensure your IP is whitelisted in Atlas")
        return False
    except pymongo.errors.OperationFailure as e:
        print(f"❌ Authentication failed: {e}")
        print("🔧 Check your username and password")
        return False
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 MongoDB Atlas Connection Test")
    print("=" * 40)
    
    if test_atlas_connection():
        print("\n🎉 Atlas connection is working perfectly!")
        print("💡 You can now run: ./run.sh")
    else:
        print("\n⚠️  Atlas connection failed")
        print("💡 Run: python3 configure_atlas.py to setup connection")
