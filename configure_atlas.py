#!/usr/bin/env python3
"""
MongoDB Atlas Configuration Helper
This script helps you configure your MongoDB Atlas connection
"""

import os
import re

def configure_atlas():
    print("🔧 MongoDB Atlas Configuration Helper")
    print("=" * 50)
    
    print("\n📋 Please provide your MongoDB Atlas connection details:")
    print("You can find these in your MongoDB Atlas dashboard under 'Connect' > 'Connect your application'")
    
    # Get connection details
    cluster_url = input("\n🌐 Enter your cluster URL (e.g., cluster0.abc123.mongodb.net): ").strip()
    username = input("👤 Enter your database username: ").strip()
    password = input("🔐 Enter your database password: ").strip()
    database_name = input("📊 Enter database name (press Enter for 'chatbot_db'): ").strip() or "chatbot_db"
    
    # Construct connection string
    connection_string = f"mongodb+srv://{username}:{password}@{cluster_url}/{database_name}?retryWrites=true&w=majority"
    
    print(f"\n✅ Generated connection string:")
    print(f"mongodb+srv://{username}:{'*' * len(password)}@{cluster_url}/{database_name}?retryWrites=true&w=majority")
    
    # Update .env file
    env_content = f"""FLASK_ENV=development
SECRET_KEY=dev-secret-key-change-in-production
JWT_SECRET_KEY=jwt-secret-change-in-production
MONGODB_URI={connection_string}
RASA_URL=http://localhost:5005"""
    
    # Write to backend/.env
    with open('backend/.env', 'w') as f:
        f.write(env_content)
    
    print(f"\n✅ Updated backend/.env file")
    
    # Test connection
    print(f"\n🔍 Testing connection...")
    try:
        import pymongo
        client = pymongo.MongoClient(connection_string, serverSelectionTimeoutMS=5000)
        client.admin.command('ping')
        print("✅ Connection successful!")
        
        # List databases
        db_list = client.list_database_names()
        print(f"📊 Available databases: {db_list}")
        
        client.close()
        return True
        
    except ImportError:
        print("⚠️  PyMongo not installed. Install with: pip install pymongo")
        return False
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        print("\n🔧 Troubleshooting tips:")
        print("1. Check your username and password")
        print("2. Ensure your IP address is whitelisted in Atlas")
        print("3. Verify the cluster URL is correct")
        print("4. Make sure the database user has read/write permissions")
        return False

def show_atlas_setup_guide():
    print("\n📚 MongoDB Atlas Setup Guide:")
    print("=" * 40)
    print("1. Go to https://cloud.mongodb.com/")
    print("2. Create a free account or sign in")
    print("3. Create a new cluster (free tier is fine)")
    print("4. Create a database user:")
    print("   - Go to Database Access")
    print("   - Add New Database User")
    print("   - Choose password authentication")
    print("   - Give read/write access to any database")
    print("5. Whitelist your IP:")
    print("   - Go to Network Access")
    print("   - Add IP Address")
    print("   - Add your current IP or 0.0.0.0/0 for all IPs")
    print("6. Get connection string:")
    print("   - Go to Clusters")
    print("   - Click 'Connect'")
    print("   - Choose 'Connect your application'")
    print("   - Copy the connection string")

if __name__ == "__main__":
    print("🚀 MongoDB Atlas Configuration")
    print("\nChoose an option:")
    print("1. Configure Atlas connection")
    print("2. Show Atlas setup guide")
    
    choice = input("\nEnter your choice (1 or 2): ").strip()
    
    if choice == "1":
        if configure_atlas():
            print(f"\n🎉 Configuration complete!")
            print(f"You can now run: ./run.sh")
        else:
            print(f"\n⚠️  Configuration incomplete. Please check the connection details.")
    elif choice == "2":
        show_atlas_setup_guide()
    else:
        print("Invalid choice. Please run the script again.")
