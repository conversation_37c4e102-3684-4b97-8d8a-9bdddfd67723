#!/bin/bash

# Simple startup script for AI Academic Chatbot
echo "🚀 Starting AI Academic Chatbot..."

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}Starting Backend...${NC}"
cd backend
/Library/Frameworks/Python.framework/Versions/3.12/bin/python3 simple_app.py &
BACKEND_PID=$!
cd ..

echo -e "${BLUE}Starting Frontend...${NC}"
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

echo -e "${GREEN}✅ System Started!${NC}"
echo -e "${BLUE}Frontend: http://localhost:5173 or http://localhost:5174${NC}"
echo -e "${BLUE}Backend: http://localhost:5000${NC}"
echo -e "${BLUE}Health Check: http://localhost:5000/api/health${NC}"

echo -e "\nPress Ctrl+C to stop all services"

# Cleanup function
cleanup() {
    echo -e "\n${BLUE}Stopping services...${NC}"
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    exit 0
}

trap cleanup INT
wait
