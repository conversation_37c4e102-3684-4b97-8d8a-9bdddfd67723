#!/bin/bash

# AI Academic Chatbot with Rasa & MongoDB
echo "🚀 Starting AI Academic Chatbot with Rasa & MongoDB..."

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Check if MongoDB is accessible (local or Atlas)
check_mongodb() {
    echo -e "${BLUE}Checking MongoDB connection...${NC}"

    # Check if using Atlas (connection string contains mongodb+srv)
    if grep -q "mongodb+srv" backend/.env 2>/dev/null; then
        echo -e "${BLUE}🌐 Detected MongoDB Atlas configuration${NC}"
        return 0
    # Check local MongoDB
    elif lsof -i :27017 >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Local MongoDB is running${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️  No MongoDB connection detected${NC}"
        return 1
    fi
}

# Setup database
setup_database() {
    echo -e "${BLUE}Setting up database...${NC}"
    /Library/Frameworks/Python.framework/Versions/3.12/bin/python3 setup_db.py
}

# Train Rasa model
train_rasa() {
    echo -e "${BLUE}Training Rasa model...${NC}"
    cd chatbot
    rasa train --quiet 2>/dev/null || {
        echo -e "${YELLOW}⚠️  Rasa training failed or not installed. Using fallback responses.${NC}"
        cd ..
        return 1
    }
    cd ..
    return 0
}

# Start services
echo -e "${BLUE}Checking MongoDB...${NC}"
if check_mongodb; then
    setup_database
else
    echo -e "${YELLOW}No MongoDB connection found.${NC}"
    echo -e "${BLUE}Options:${NC}"
    echo -e "  1. Configure MongoDB Atlas: ${YELLOW}python3 configure_atlas.py${NC}"
    echo -e "  2. Install local MongoDB: ${YELLOW}brew install mongodb-community${NC}"
    echo -e "  3. Continue without MongoDB (uses in-memory storage)"
    echo -e "${YELLOW}   The system will work with in-memory storage as fallback.${NC}"
fi

echo -e "${BLUE}Training Rasa model...${NC}"
RASA_TRAINED=false
if train_rasa; then
    RASA_TRAINED=true
    echo -e "${GREEN}✅ Rasa model trained successfully${NC}"
else
    echo -e "${YELLOW}⚠️  Using fallback responses instead of Rasa${NC}"
fi

echo -e "${BLUE}Starting Backend...${NC}"
cd backend
/Library/Frameworks/Python.framework/Versions/3.12/bin/python3 simple_app.py &
BACKEND_PID=$!
cd ..

# Start Rasa if trained successfully
if [ "$RASA_TRAINED" = true ]; then
    echo -e "${BLUE}Starting Rasa server...${NC}"
    cd chatbot
    rasa run --enable-api --cors "*" --quiet &
    RASA_PID=$!

    echo -e "${BLUE}Starting Rasa actions server...${NC}"
    rasa run actions --quiet &
    ACTIONS_PID=$!
    cd ..
else
    RASA_PID=""
    ACTIONS_PID=""
fi

echo -e "${BLUE}Starting Frontend...${NC}"
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

echo -e "${GREEN}🎉 System Started!${NC}"
echo -e "${BLUE}📱 Frontend: http://localhost:5173 or http://localhost:5174${NC}"
echo -e "${BLUE}🔧 Backend: http://localhost:5000${NC}"
echo -e "${BLUE}📊 Health Check: http://localhost:5000/api/health${NC}"

if [ "$RASA_TRAINED" = true ]; then
    echo -e "${BLUE}🤖 Rasa: http://localhost:5005${NC}"
    echo -e "${BLUE}⚡ Actions: http://localhost:5055${NC}"
fi

echo -e "\n${GREEN}Press Ctrl+C to stop all services${NC}"

# Cleanup function
cleanup() {
    echo -e "\n${BLUE}Stopping services...${NC}"
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    [ -n "$RASA_PID" ] && kill $RASA_PID 2>/dev/null
    [ -n "$ACTIONS_PID" ] && kill $ACTIONS_PID 2>/dev/null
    exit 0
}

trap cleanup INT
wait
