.message-bubble {
  display: flex;
  gap: 0.75rem;
  max-width: 85%;
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bot-message {
  align-self: flex-start;
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 0.25rem;
}

.bot-message .message-avatar {
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-message .message-avatar {
  background: rgba(102, 126, 234, 0.9);
}

.avatar-icon {
  width: 1.25rem;
  height: 1.25rem;
}

.bot-message .avatar-icon {
  color: #667eea;
}

.bot-message .avatar-icon.error {
  color: #e53e3e;
}

.user-message .avatar-icon {
  color: white;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.25rem;
  gap: 0.5rem;
}

.message-sender {
  font-size: 0.75rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
}

.message-timestamp {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  flex-shrink: 0;
}

.message-text {
  padding: 0.75rem 1rem;
  border-radius: 1.25rem;
  font-size: 0.95rem;
  line-height: 1.5;
  word-wrap: break-word;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.bot-message .message-text {
  background: rgba(255, 255, 255, 0.95);
  color: #2d3748;
  border-bottom-left-radius: 0.5rem;
}

.user-message .message-text {
  background: rgba(102, 126, 234, 0.9);
  color: white;
  border-bottom-right-radius: 0.5rem;
}

.message-text.error-message {
  background: rgba(254, 178, 178, 0.9);
  color: #742a2a;
  border: 1px solid rgba(245, 101, 101, 0.3);
}

.message-text strong {
  font-weight: 600;
}

.message-text em {
  font-style: italic;
}

.message-metadata {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
  font-size: 0.75rem;
}

.message-intent {
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 0.75rem;
  font-weight: 500;
}

.message-confidence {
  padding: 0.25rem 0.5rem;
  border-radius: 0.75rem;
  font-weight: 500;
}

.high-confidence {
  background: rgba(72, 187, 120, 0.2);
  color: #38a169;
}

.medium-confidence {
  background: rgba(237, 137, 54, 0.2);
  color: #dd6b20;
}

.low-confidence {
  background: rgba(245, 101, 101, 0.2);
  color: #e53e3e;
}

.error-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.75rem;
}

.error-icon {
  width: 1rem;
  height: 1rem;
  color: #e53e3e;
}

/* Responsive design */
@media (max-width: 768px) {
  .message-bubble {
    max-width: 95%;
    gap: 0.5rem;
  }
  
  .message-avatar {
    width: 2rem;
    height: 2rem;
  }
  
  .avatar-icon {
    width: 1rem;
    height: 1rem;
  }
  
  .message-text {
    padding: 0.625rem 0.875rem;
    font-size: 0.9rem;
  }
  
  .message-metadata {
    flex-direction: column;
    gap: 0.25rem;
  }
}
