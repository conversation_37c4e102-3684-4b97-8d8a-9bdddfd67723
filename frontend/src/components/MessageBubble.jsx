import React from 'react';
import { Bo<PERSON>, User, AlertCircle } from 'lucide-react';
import './MessageBubble.css';

const MessageBubble = ({ message, user }) => {
  const { text, sender, timestamp, confidence, intent, isError } = message;
  const isBot = sender === 'bot';
  const isUser = sender === 'user';

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getConfidenceColor = (confidence) => {
    if (!confidence) return '';
    if (confidence >= 0.8) return 'high-confidence';
    if (confidence >= 0.6) return 'medium-confidence';
    return 'low-confidence';
  };

  const formatMessageText = (text) => {
    // Simple formatting for common patterns
    return text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Bold
      .replace(/\*(.*?)\*/g, '<em>$1</em>') // Italic
      .replace(/\n/g, '<br>'); // Line breaks
  };

  return (
    <div className={`message-bubble ${isBot ? 'bot-message' : 'user-message'}`}>
      <div className="message-avatar">
        {isBot ? (
          <Bot className={`avatar-icon ${isError ? 'error' : ''}`} />
        ) : (
          <User className="avatar-icon" />
        )}
      </div>
      
      <div className="message-content">
        <div className="message-header">
          <span className="message-sender">
            {isBot ? 'Academic Assistant' : user?.name || 'You'}
          </span>
          <span className="message-timestamp">
            {formatTimestamp(timestamp)}
          </span>
        </div>
        
        <div 
          className={`message-text ${isError ? 'error-message' : ''}`}
          dangerouslySetInnerHTML={{ __html: formatMessageText(text) }}
        />
        
        {/* Show confidence and intent for bot messages */}
        {isBot && (confidence || intent) && !isError && (
          <div className="message-metadata">
            {intent && (
              <span className="message-intent">
                Intent: {intent}
              </span>
            )}
            {confidence && (
              <span className={`message-confidence ${getConfidenceColor(confidence)}`}>
                Confidence: {Math.round(confidence * 100)}%
              </span>
            )}
          </div>
        )}
        
        {/* Error indicator */}
        {isError && (
          <div className="error-indicator">
            <AlertCircle className="error-icon" />
            <span>Message failed to send</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default MessageBubble;
