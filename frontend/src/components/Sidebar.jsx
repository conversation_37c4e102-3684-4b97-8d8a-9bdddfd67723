import React, { useState, useEffect } from 'react';
import { Plus, MessageSquare, LogOut, User, Trash2, Menu, X } from 'lucide-react';
import { chatAPI } from '../services/api';
import './Sidebar.css';

const Sidebar = ({ user, onNewChat, onSelectChat, currentChatId, onLogout }) => {
  const [conversations, setConversations] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);

  useEffect(() => {
    loadConversations();
  }, []);

  const loadConversations = async () => {
    try {
      setIsLoading(true);
      const response = await chatAPI.getConversations();
      setConversations(response.conversations || []);
    } catch (error) {
      console.error('Error loading conversations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewChat = async () => {
    try {
      const response = await chatAPI.createConversation('New Conversation');
      const newConversation = {
        id: response.conversation_id,
        title: response.title,
        created_at: new Date().toISOString(),
        last_activity: new Date().toISOString(),
        last_message: null
      };
      
      setConversations(prev => [newConversation, ...prev]);
      onNewChat(response.conversation_id);
    } catch (error) {
      console.error('Error creating new chat:', error);
      // Fallback: create a local conversation
      const newConversation = {
        id: `conv_${Date.now()}`,
        title: 'New Conversation',
        created_at: new Date().toISOString(),
        last_activity: new Date().toISOString(),
        last_message: null
      };
      setConversations(prev => [newConversation, ...prev]);
      onNewChat(newConversation.id);
    }
  };

  const handleDeleteChat = async (chatId, e) => {
    e.stopPropagation();
    
    if (!confirm('Are you sure you want to delete this conversation?')) {
      return;
    }

    try {
      await chatAPI.deleteConversation(chatId);
      setConversations(prev => prev.filter(conv => conv.id !== chatId));
      
      // If we deleted the current chat, start a new one
      if (chatId === currentChatId) {
        handleNewChat();
      }
    } catch (error) {
      console.error('Error deleting conversation:', error);
      // Still remove from UI even if API call fails
      setConversations(prev => prev.filter(conv => conv.id !== chatId));
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Today';
    if (diffDays === 2) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays - 1} days ago`;
    return date.toLocaleDateString();
  };

  const truncateMessage = (message, maxLength = 50) => {
    if (!message) return 'No messages yet';
    return message.length > maxLength ? message.substring(0, maxLength) + '...' : message;
  };

  return (
    <>
      {/* Mobile toggle button */}
      <button 
        className="sidebar-toggle"
        onClick={() => setIsCollapsed(!isCollapsed)}
      >
        {isCollapsed ? <Menu /> : <X />}
      </button>

      <div className={`sidebar ${isCollapsed ? 'collapsed' : ''}`}>
        {/* User Profile Section */}
        <div className="sidebar-header">
          <div className="user-profile">
            <div className="user-avatar">
              <User className="user-icon" />
            </div>
            <div className="user-info">
              <h3>{user?.name || 'Student'}</h3>
              <p>{user?.program || 'Academic Program'}</p>
            </div>
          </div>
        </div>

        {/* New Chat Button */}
        <div className="sidebar-actions">
          <button className="new-chat-btn" onClick={handleNewChat}>
            <Plus className="btn-icon" />
            <span>New Chat</span>
          </button>
        </div>

        {/* Chat History */}
        <div className="chat-history">
          <h4>Chat History</h4>
          
          {isLoading ? (
            <div className="loading-conversations">
              <div className="loading-spinner"></div>
              <p>Loading conversations...</p>
            </div>
          ) : conversations.length === 0 ? (
            <div className="no-conversations">
              <MessageSquare className="empty-icon" />
              <p>No conversations yet</p>
              <small>Start a new chat to begin</small>
            </div>
          ) : (
            <div className="conversations-list">
              {conversations.map((conversation) => (
                <div
                  key={conversation.id}
                  className={`conversation-item ${conversation.id === currentChatId ? 'active' : ''}`}
                  onClick={() => onSelectChat(conversation.id)}
                >
                  <div className="conversation-content">
                    <div className="conversation-header">
                      <h5>{conversation.title}</h5>
                      <span className="conversation-date">
                        {formatDate(conversation.last_activity)}
                      </span>
                    </div>
                    <p className="conversation-preview">
                      {truncateMessage(conversation.last_message)}
                    </p>
                  </div>
                  <button
                    className="delete-chat-btn"
                    onClick={(e) => handleDeleteChat(conversation.id, e)}
                    title="Delete conversation"
                  >
                    <Trash2 className="delete-icon" />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Logout Button */}
        <div className="sidebar-footer">
          <button className="logout-btn" onClick={onLogout}>
            <LogOut className="btn-icon" />
            <span>Logout</span>
          </button>
        </div>
      </div>

      {/* Overlay for mobile */}
      {!isCollapsed && (
        <div 
          className="sidebar-overlay"
          onClick={() => setIsCollapsed(true)}
        />
      )}
    </>
  );
};

export default Sidebar;
