import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

// Create axios instance with default config
const api = axios.create({
  baseURL: `${API_BASE_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API calls
export const authAPI = {
  login: async (credentials) => {
    const response = await api.post('/auth/login', credentials);
    return response.data;
  },
  
  register: async (userData) => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },
  
  logout: async () => {
    const response = await api.post('/auth/logout');
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    return response.data;
  },
  
  getProfile: async () => {
    const response = await api.get('/auth/profile');
    return response.data;
  }
};

// Chat API calls
export const chatAPI = {
  sendMessage: async (message, conversationId = null) => {
    const response = await api.post('/chat', {
      message,
      conversation_id: conversationId
    });
    return response.data;
  },
  
  getChatHistory: async (conversationId = null) => {
    const params = conversationId ? { conversation_id: conversationId } : {};
    const response = await api.get('/chat/history', { params });
    return response.data;
  },
  
  getConversations: async () => {
    const response = await api.get('/chat/conversations');
    return response.data;
  },
  
  createConversation: async (title = 'New Conversation') => {
    const response = await api.post('/chat/conversations', { title });
    return response.data;
  },
  
  deleteConversation: async (conversationId) => {
    const response = await api.delete(`/chat/conversations/${conversationId}`);
    return response.data;
  }
};

// Academic data API calls
export const academicAPI = {
  getCourses: async () => {
    const response = await api.get('/academic/courses');
    return response.data;
  },
  
  getCourse: async (courseId) => {
    const response = await api.get(`/academic/courses/${courseId}`);
    return response.data;
  },
  
  getSchedule: async (studentId = null) => {
    const params = studentId ? { student_id: studentId } : {};
    const response = await api.get('/academic/schedule', { params });
    return response.data;
  },
  
  getAssignments: async (courseId = null) => {
    const params = courseId ? { course_id: courseId } : {};
    const response = await api.get('/academic/assignments', { params });
    return response.data;
  },
  
  getLecturers: async () => {
    const response = await api.get('/academic/lecturers');
    return response.data;
  }
};

export default api;
