#!/usr/bin/env python3
"""
Simple MongoDB setup script for Academic Chatbot
"""

import pymongo
from datetime import datetime, timedelta
import os

# MongoDB connection
MONGODB_URI = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/chatbot_db')

def setup_database():
    """Setup MongoDB with sample data"""
    try:
        print(f"🔗 Connecting to MongoDB...")
        client = pymongo.MongoClient(MONGODB_URI, serverSelectionTimeoutMS=10000)

        # Test connection
        client.admin.command('ping')
        print("✅ Connected to MongoDB successfully")

        # Get database
        if 'chatbot_db' in MONGODB_URI:
            db_name = 'chatbot_db'
        else:
            db_name = client.get_default_database().name

        db = client[db_name]
        print(f"📊 Using database: {db_name}")

        # Clear existing data
        collections = ['courses', 'lecturers', 'assignments', 'schedules']
        for collection in collections:
            count = db[collection].count_documents({})
            if count > 0:
                db[collection].delete_many({})
                print(f"✅ Cleared {count} documents from {collection} collection")
            else:
                print(f"📝 {collection} collection is empty")
        
        # Sample lecturers
        lecturers = [
            {
                'name': 'Dr. <PERSON>',
                'email': '<EMAIL>',
                'office': 'CS Building, Room 301',
                'office_hours': 'Mon & Wed 2:00-4:00 PM',
                'department': 'Computer Science'
            },
            {
                'name': 'Prof. Michael Chen',
                'email': '<EMAIL>',
                'office': 'Math Building, Room 205',
                'office_hours': 'Tue & Thu 1:00-3:00 PM',
                'department': 'Mathematics'
            },
            {
                'name': 'Prof. David Wilson',
                'email': '<EMAIL>',
                'office': 'English Building, Room 102',
                'office_hours': 'Tue & Thu 3:00-5:00 PM',
                'department': 'English'
            }
        ]
        
        db.lecturers.insert_many(lecturers)
        print(f"✅ Inserted {len(lecturers)} lecturers")
        
        # Sample courses
        courses = [
            {
                'course_code': 'CS101',
                'course_name': 'Introduction to Computer Science',
                'credits': 3,
                'lecturer': 'Dr. Sarah Johnson',
                'department': 'Computer Science'
            },
            {
                'course_code': 'MATH201',
                'course_name': 'Calculus I',
                'credits': 4,
                'lecturer': 'Prof. Michael Chen',
                'department': 'Mathematics'
            },
            {
                'course_code': 'ENG101',
                'course_name': 'English Composition',
                'credits': 3,
                'lecturer': 'Prof. David Wilson',
                'department': 'English'
            }
        ]
        
        db.courses.insert_many(courses)
        print(f"✅ Inserted {len(courses)} courses")
        
        # Sample assignments
        base_date = datetime.now()
        assignments = [
            {
                'title': 'Programming Assignment 1',
                'course': 'CS101',
                'due_date': (base_date + timedelta(days=7)).strftime('%Y-%m-%d'),
                'description': 'Basic programming concepts'
            },
            {
                'title': 'Calculus Problem Set 1',
                'course': 'MATH201',
                'due_date': (base_date + timedelta(days=10)).strftime('%Y-%m-%d'),
                'description': 'Limits and derivatives'
            },
            {
                'title': 'Essay Assignment',
                'course': 'ENG101',
                'due_date': (base_date + timedelta(days=14)).strftime('%Y-%m-%d'),
                'description': 'Personal narrative essay'
            }
        ]
        
        db.assignments.insert_many(assignments)
        print(f"✅ Inserted {len(assignments)} assignments")
        
        # Sample schedules
        schedules = [
            {
                'course_code': 'CS101',
                'day_of_week': 'Monday',
                'start_time': '09:00',
                'end_time': '10:00',
                'room': 'CS-101'
            },
            {
                'course_code': 'CS101',
                'day_of_week': 'Wednesday',
                'start_time': '09:00',
                'end_time': '10:00',
                'room': 'CS-101'
            },
            {
                'course_code': 'MATH201',
                'day_of_week': 'Tuesday',
                'start_time': '11:00',
                'end_time': '12:30',
                'room': 'MATH-105'
            },
            {
                'course_code': 'MATH201',
                'day_of_week': 'Thursday',
                'start_time': '11:00',
                'end_time': '12:30',
                'room': 'MATH-105'
            },
            {
                'course_code': 'ENG101',
                'day_of_week': 'Monday',
                'start_time': '14:00',
                'end_time': '15:00',
                'room': 'ENG-201'
            },
            {
                'course_code': 'ENG101',
                'day_of_week': 'Wednesday',
                'start_time': '14:00',
                'end_time': '15:00',
                'room': 'ENG-201'
            }
        ]
        
        db.schedules.insert_many(schedules)
        print(f"✅ Inserted {len(schedules)} schedule entries")
        
        print("\n🎉 Database setup completed successfully!")
        print("📊 Sample data includes:")
        print(f"   - {len(lecturers)} lecturers")
        print(f"   - {len(courses)} courses")
        print(f"   - {len(assignments)} assignments")
        print(f"   - {len(schedules)} schedule entries")

        # Show database info
        print(f"\n📈 Database Statistics:")
        for collection in collections:
            count = db[collection].count_documents({})
            print(f"   - {collection}: {count} documents")

        client.close()

    except pymongo.errors.ServerSelectionTimeoutError:
        print(f"❌ Could not connect to MongoDB")
        print(f"🔧 Troubleshooting:")
        print(f"   - Check your internet connection")
        print(f"   - Verify MongoDB Atlas connection string")
        print(f"   - Ensure IP address is whitelisted")
        print(f"   - Run: python3 configure_atlas.py")
        return False
    except pymongo.errors.OperationFailure as e:
        print(f"❌ Database operation failed: {e}")
        print(f"🔧 Check your database user permissions")
        return False
    except Exception as e:
        print(f"❌ Error setting up database: {e}")
        return False

    return True

if __name__ == "__main__":
    print("🚀 Setting up MongoDB for Academic Chatbot...")
    setup_database()
