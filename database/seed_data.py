#!/usr/bin/env python3
"""
Database seeding script for the Academic Chatbot
Populates the database with sample academic data
"""

import os
import sys
from datetime import datetime, timedelta
from pymongo import MongoClient
from bson import ObjectId

# MongoDB connection
MONGODB_URI = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/chatbot_db')

def connect_to_db():
    """Connect to MongoDB"""
    try:
        client = MongoClient(MONGODB_URI)
        db = client.get_default_database()
        # Test connection
        client.admin.command('ping')
        print("Successfully connected to MongoDB")
        return db
    except Exception as e:
        print(f"Failed to connect to MongoDB: {e}")
        sys.exit(1)

def clear_collections(db):
    """Clear existing data"""
    collections = ['courses', 'lecturers', 'assignments', 'schedules', 'enrollments', 'exams']
    for collection in collections:
        db[collection].delete_many({})
        print(f"Cleared {collection} collection")

def seed_lecturers(db):
    """Seed lecturer data"""
    lecturers = [
        {
            'name': 'Dr. <PERSON>',
            'email': '<EMAIL>',
            'phone': '******-0101',
            'office': 'CS Building, Room 301',
            'department': 'Computer Science',
            'office_hours': 'Monday & Wednesday 2:00-4:00 PM',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'name': 'Prof. Michael Chen',
            'email': '<EMAIL>',
            'phone': '******-0102',
            'office': 'Math Building, Room 205',
            'department': 'Mathematics',
            'office_hours': 'Tuesday & Thursday 1:00-3:00 PM',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'name': 'Dr. Emily Rodriguez',
            'email': '<EMAIL>',
            'phone': '******-0103',
            'office': 'Physics Building, Room 401',
            'department': 'Physics',
            'office_hours': 'Monday, Wednesday & Friday 10:00-11:00 AM',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'name': 'Prof. David Wilson',
            'email': '<EMAIL>',
            'phone': '******-0104',
            'office': 'English Building, Room 102',
            'department': 'English',
            'office_hours': 'Tuesday & Thursday 3:00-5:00 PM',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'name': 'Dr. Lisa Thompson',
            'email': '<EMAIL>',
            'phone': '******-0105',
            'office': 'Chemistry Building, Room 303',
            'department': 'Chemistry',
            'office_hours': 'Monday & Wednesday 11:00 AM-1:00 PM',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
    ]
    
    result = db.lecturers.insert_many(lecturers)
    print(f"Inserted {len(result.inserted_ids)} lecturers")
    return result.inserted_ids

def seed_courses(db, lecturer_ids):
    """Seed course data"""
    courses = [
        {
            'course_code': 'CS101',
            'course_name': 'Introduction to Computer Science',
            'description': 'Fundamental concepts of computer science including programming basics, algorithms, and data structures.',
            'credits': 3,
            'department': 'Computer Science',
            'lecturer_id': lecturer_ids[0],
            'semester': 'Fall',
            'year': '2024',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'course_code': 'CS102',
            'course_name': 'Data Structures and Algorithms',
            'description': 'Advanced data structures, algorithm design and analysis, complexity theory.',
            'credits': 4,
            'department': 'Computer Science',
            'lecturer_id': lecturer_ids[0],
            'semester': 'Spring',
            'year': '2024',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'course_code': 'MATH201',
            'course_name': 'Calculus I',
            'description': 'Limits, derivatives, applications of derivatives, and introduction to integrals.',
            'credits': 4,
            'department': 'Mathematics',
            'lecturer_id': lecturer_ids[1],
            'semester': 'Fall',
            'year': '2024',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'course_code': 'MATH202',
            'course_name': 'Calculus II',
            'description': 'Integration techniques, applications of integrals, infinite series.',
            'credits': 4,
            'department': 'Mathematics',
            'lecturer_id': lecturer_ids[1],
            'semester': 'Spring',
            'year': '2024',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'course_code': 'PHYS101',
            'course_name': 'General Physics I',
            'description': 'Mechanics, waves, and thermodynamics with laboratory component.',
            'credits': 4,
            'department': 'Physics',
            'lecturer_id': lecturer_ids[2],
            'semester': 'Fall',
            'year': '2024',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'course_code': 'ENG101',
            'course_name': 'English Composition',
            'description': 'Academic writing, research methods, and critical thinking skills.',
            'credits': 3,
            'department': 'English',
            'lecturer_id': lecturer_ids[3],
            'semester': 'Fall',
            'year': '2024',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'course_code': 'CHEM101',
            'course_name': 'General Chemistry',
            'description': 'Atomic structure, chemical bonding, stoichiometry, and basic reactions.',
            'credits': 4,
            'department': 'Chemistry',
            'lecturer_id': lecturer_ids[4],
            'semester': 'Fall',
            'year': '2024',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
    ]
    
    result = db.courses.insert_many(courses)
    print(f"Inserted {len(result.inserted_ids)} courses")
    return result.inserted_ids

def seed_schedules(db, course_ids):
    """Seed schedule data"""
    schedules = [
        # CS101 - MWF 9:00-10:00
        {
            'course_id': course_ids[0],
            'day_of_week': 'Monday',
            'start_time': '09:00',
            'end_time': '10:00',
            'room': 'CS-101',
            'building': 'Computer Science Building',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'course_id': course_ids[0],
            'day_of_week': 'Wednesday',
            'start_time': '09:00',
            'end_time': '10:00',
            'room': 'CS-101',
            'building': 'Computer Science Building',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'course_id': course_ids[0],
            'day_of_week': 'Friday',
            'start_time': '09:00',
            'end_time': '10:00',
            'room': 'CS-101',
            'building': 'Computer Science Building',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        # CS102 - TTh 11:00-12:30
        {
            'course_id': course_ids[1],
            'day_of_week': 'Tuesday',
            'start_time': '11:00',
            'end_time': '12:30',
            'room': 'CS-201',
            'building': 'Computer Science Building',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'course_id': course_ids[1],
            'day_of_week': 'Thursday',
            'start_time': '11:00',
            'end_time': '12:30',
            'room': 'CS-201',
            'building': 'Computer Science Building',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        # MATH201 - MWF 10:00-11:00
        {
            'course_id': course_ids[2],
            'day_of_week': 'Monday',
            'start_time': '10:00',
            'end_time': '11:00',
            'room': 'MATH-105',
            'building': 'Mathematics Building',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'course_id': course_ids[2],
            'day_of_week': 'Wednesday',
            'start_time': '10:00',
            'end_time': '11:00',
            'room': 'MATH-105',
            'building': 'Mathematics Building',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'course_id': course_ids[2],
            'day_of_week': 'Friday',
            'start_time': '10:00',
            'end_time': '11:00',
            'room': 'MATH-105',
            'building': 'Mathematics Building',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
    ]
    
    result = db.schedules.insert_many(schedules)
    print(f"Inserted {len(result.inserted_ids)} schedule entries")
    return result.inserted_ids

def seed_assignments(db, course_ids):
    """Seed assignment data"""
    base_date = datetime.utcnow()

    assignments = [
        {
            'course_id': course_ids[0],  # CS101
            'title': 'Programming Assignment 1: Hello World',
            'description': 'Write your first program that prints "Hello, World!" and demonstrates basic input/output.',
            'due_date': base_date + timedelta(days=7),
            'points': 50,
            'status': 'active',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'course_id': course_ids[0],  # CS101
            'title': 'Programming Assignment 2: Variables and Control Flow',
            'description': 'Create a program that uses variables, conditionals, and loops to solve basic problems.',
            'due_date': base_date + timedelta(days=14),
            'points': 75,
            'status': 'active',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'course_id': course_ids[1],  # CS102
            'title': 'Data Structures Project: Linked Lists',
            'description': 'Implement a linked list data structure with insertion, deletion, and search operations.',
            'due_date': base_date + timedelta(days=21),
            'points': 100,
            'status': 'active',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'course_id': course_ids[2],  # MATH201
            'title': 'Calculus Problem Set 1',
            'description': 'Solve problems involving limits and continuity from Chapter 2.',
            'due_date': base_date + timedelta(days=10),
            'points': 60,
            'status': 'active',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'course_id': course_ids[5],  # ENG101
            'title': 'Essay 1: Personal Narrative',
            'description': 'Write a 500-word personal narrative essay demonstrating proper grammar and structure.',
            'due_date': base_date + timedelta(days=12),
            'points': 80,
            'status': 'active',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
    ]

    result = db.assignments.insert_many(assignments)
    print(f"Inserted {len(result.inserted_ids)} assignments")
    return result.inserted_ids

def seed_sample_enrollments(db, course_ids):
    """Seed sample enrollment data for testing"""
    # Sample student ID for testing
    sample_student_id = "STU001"

    enrollments = [
        {
            'student_id': sample_student_id,
            'course_id': course_ids[0],  # CS101
            'enrollment_date': datetime.utcnow() - timedelta(days=30),
            'status': 'enrolled',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'student_id': sample_student_id,
            'course_id': course_ids[2],  # MATH201
            'enrollment_date': datetime.utcnow() - timedelta(days=30),
            'status': 'enrolled',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'student_id': sample_student_id,
            'course_id': course_ids[5],  # ENG101
            'enrollment_date': datetime.utcnow() - timedelta(days=30),
            'status': 'enrolled',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
    ]

    result = db.enrollments.insert_many(enrollments)
    print(f"Inserted {len(result.inserted_ids)} sample enrollments")
    return result.inserted_ids

def seed_exams(db, course_ids):
    """Seed exam data"""
    base_date = datetime.utcnow()

    exams = [
        {
            'course_id': course_ids[0],  # CS101
            'exam_type': 'Midterm',
            'exam_date': base_date + timedelta(days=30),
            'start_time': '14:00',
            'end_time': '16:00',
            'room': 'EXAM-HALL-A',
            'building': 'Main Building',
            'instructions': 'Bring student ID and calculator. No electronic devices allowed.',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'course_id': course_ids[0],  # CS101
            'exam_type': 'Final',
            'exam_date': base_date + timedelta(days=60),
            'start_time': '09:00',
            'end_time': '12:00',
            'room': 'EXAM-HALL-B',
            'building': 'Main Building',
            'instructions': 'Comprehensive exam covering all course material. Bring student ID.',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        },
        {
            'course_id': course_ids[2],  # MATH201
            'exam_type': 'Midterm',
            'exam_date': base_date + timedelta(days=35),
            'start_time': '10:00',
            'end_time': '12:00',
            'room': 'MATH-HALL',
            'building': 'Mathematics Building',
            'instructions': 'Calculator allowed. Show all work for partial credit.',
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
    ]

    result = db.exams.insert_many(exams)
    print(f"Inserted {len(result.inserted_ids)} exams")
    return result.inserted_ids

def main():
    """Main seeding function"""
    print("Starting database seeding...")

    # Connect to database
    db = connect_to_db()

    # Clear existing data
    print("\nClearing existing data...")
    clear_collections(db)

    # Seed data
    print("\nSeeding lecturers...")
    lecturer_ids = seed_lecturers(db)

    print("\nSeeding courses...")
    course_ids = seed_courses(db, lecturer_ids)

    print("\nSeeding schedules...")
    schedule_ids = seed_schedules(db, course_ids)

    print("\nSeeding assignments...")
    assignment_ids = seed_assignments(db, course_ids)

    print("\nSeeding sample enrollments...")
    enrollment_ids = seed_sample_enrollments(db, course_ids)

    print("\nSeeding exams...")
    exam_ids = seed_exams(db, course_ids)

    print("\n✅ Database seeding completed successfully!")
    print(f"Created {len(lecturer_ids)} lecturers, {len(course_ids)} courses, {len(schedule_ids)} schedules,")
    print(f"{len(assignment_ids)} assignments, {len(enrollment_ids)} enrollments, and {len(exam_ids)} exams.")

if __name__ == "__main__":
    main()
