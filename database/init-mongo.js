// MongoDB initialization script for Docker
// This script runs when the MongoDB container starts for the first time

// Switch to the chatbot database
db = db.getSiblingDB('chatbot_db');

// Create collections with validation
db.createCollection('users', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['email', 'name', 'student_id', 'program', 'year', 'password_hash'],
      properties: {
        email: {
          bsonType: 'string',
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        },
        name: {
          bsonType: 'string',
          minLength: 1
        },
        student_id: {
          bsonType: 'string',
          minLength: 1
        },
        program: {
          bsonType: 'string',
          minLength: 1
        },
        year: {
          bsonType: 'int',
          minimum: 1,
          maximum: 5
        },
        password_hash: {
          bsonType: 'binData'
        }
      }
    }
  }
});

db.createCollection('conversations');
db.createCollection('messages');
db.createCollection('courses');
db.createCollection('lecturers');
db.createCollection('assignments');
db.createCollection('schedules');
db.createCollection('enrollments');
db.createCollection('exams');

// Create indexes
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ student_id: 1 }, { unique: true });
db.conversations.createIndex({ user_id: 1, created_at: -1 });
db.messages.createIndex({ conversation_id: 1, timestamp: 1 });
db.courses.createIndex({ course_code: 1 }, { unique: true });
db.assignments.createIndex({ course_id: 1, due_date: 1 });
db.schedules.createIndex({ course_id: 1, day_of_week: 1 });
db.enrollments.createIndex({ student_id: 1, course_id: 1 });

print('Database initialization completed successfully!');
