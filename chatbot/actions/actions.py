from typing import Any, Text, Dict, List
from rasa_sdk import Action, Tracker
from rasa_sdk.executor import CollectingDispatcher
import pymongo
import os

# MongoDB connection
MONGODB_URI = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/chatbot_db')

def get_db():
    """Get MongoDB database connection"""
    try:
        client = pymongo.MongoClient(MONGODB_URI)
        db = client.get_default_database()
        return db
    except Exception as e:
        print(f"Database connection error: {e}")
        return None

class ActionGetCourses(Action):
    def name(self) -> Text:
        return "action_get_courses"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        try:
            db = get_db()
            if db is None:
                dispatcher.utter_message(text="Sorry, I can't access the course database right now.")
                return []
            
            courses = list(db.courses.find({}).limit(5))
            
            if courses:
                message = "Here are the available courses:\n\n"
                for course in courses:
                    message += f"• **{course.get('course_code', 'N/A')}** - {course.get('course_name', 'N/A')}\n"
                    message += f"  Credits: {course.get('credits', 'N/A')}\n"
                    message += f"  Instructor: {course.get('lecturer', 'N/A')}\n\n"
                
                dispatcher.utter_message(text=message)
            else:
                dispatcher.utter_message(text="No courses found in the database.")
                
        except Exception as e:
            print(f"Error getting courses: {e}")
            dispatcher.utter_message(text="Sorry, I encountered an error while fetching course information.")
        
        return []

class ActionGetAssignments(Action):
    def name(self) -> Text:
        return "action_get_assignments"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        try:
            db = get_db()
            if db is None:
                dispatcher.utter_message(text="Sorry, I can't access the assignments database right now.")
                return []
            
            assignments = list(db.assignments.find({}).limit(5))
            
            if assignments:
                message = "Here are your upcoming assignments:\n\n"
                for assignment in assignments:
                    message += f"• **{assignment.get('title', 'N/A')}**\n"
                    message += f"  Course: {assignment.get('course', 'N/A')}\n"
                    message += f"  Due: {assignment.get('due_date', 'N/A')}\n\n"
                
                dispatcher.utter_message(text=message)
            else:
                dispatcher.utter_message(text="No assignments found in the database.")
                
        except Exception as e:
            print(f"Error getting assignments: {e}")
            dispatcher.utter_message(text="Sorry, I encountered an error while fetching assignment information.")
        
        return []

class ActionGetSchedule(Action):
    def name(self) -> Text:
        return "action_get_schedule"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        try:
            db = get_db()
            if db is None:
                dispatcher.utter_message(text="Sorry, I can't access the schedule database right now.")
                return []
            
            schedules = list(db.schedules.find({}).limit(10))
            
            if schedules:
                message = "Here's your class schedule:\n\n"
                current_day = None
                
                for schedule in schedules:
                    day = schedule.get('day_of_week', 'N/A')
                    if current_day != day:
                        current_day = day
                        message += f"**{day}:**\n"
                    
                    message += f"• {schedule.get('course_code', 'N/A')} - {schedule.get('start_time', 'N/A')} to {schedule.get('end_time', 'N/A')}\n"
                    message += f"  Room: {schedule.get('room', 'N/A')}\n\n"
                
                dispatcher.utter_message(text=message)
            else:
                dispatcher.utter_message(text="No schedule found in the database.")
                
        except Exception as e:
            print(f"Error getting schedule: {e}")
            dispatcher.utter_message(text="Sorry, I encountered an error while fetching schedule information.")
        
        return []

class ActionGetLecturers(Action):
    def name(self) -> Text:
        return "action_get_lecturers"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        try:
            db = get_db()
            if db is None:
                dispatcher.utter_message(text="Sorry, I can't access the lecturers database right now.")
                return []
            
            lecturers = list(db.lecturers.find({}).limit(5))
            
            if lecturers:
                message = "Here are your lecturers:\n\n"
                for lecturer in lecturers:
                    message += f"• **{lecturer.get('name', 'N/A')}**\n"
                    message += f"  Email: {lecturer.get('email', 'N/A')}\n"
                    message += f"  Office: {lecturer.get('office', 'N/A')}\n"
                    message += f"  Office Hours: {lecturer.get('office_hours', 'N/A')}\n\n"
                
                dispatcher.utter_message(text=message)
            else:
                dispatcher.utter_message(text="No lecturer information found in the database.")
                
        except Exception as e:
            print(f"Error getting lecturers: {e}")
            dispatcher.utter_message(text="Sorry, I encountered an error while fetching lecturer information.")
        
        return []
