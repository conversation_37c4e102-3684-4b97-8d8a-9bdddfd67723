from typing import Any, Text, Dict, List
from rasa_sdk import Action, Tracker
from rasa_sdk.executor import CollectingDispatcher
from rasa_sdk.events import SlotSet
import requests
import os

# Backend API URL
BACKEND_URL = os.getenv('BACKEND_URL', 'http://localhost:5000/api')

class ActionGetCourseInfo(Action):
    def name(self) -> Text:
        return "action_get_course_info"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        course_code = tracker.get_slot("course_code")
        course_name = tracker.get_slot("course_name")
        
        if not course_code and not course_name:
            dispatcher.utter_message(text="Which course would you like information about? Please provide the course code or name.")
            return []
        
        try:
            # Get user metadata
            metadata = tracker.latest_message.get('metadata', {})
            user_id = metadata.get('user_id')
            
            if not user_id:
                dispatcher.utter_message(text="I need to verify your identity to provide course information.")
                return []
            
            # Make API call to backend
            headers = {'Authorization': f'Bearer {metadata.get("token", "")}'}
            response = requests.get(f"{BACKEND_URL}/academic/courses", headers=headers)
            
            if response.status_code == 200:
                courses = response.json().get('courses', [])
                
                # Find matching course
                matching_course = None
                for course in courses:
                    if (course_code and course['course_code'].lower() == course_code.lower()) or \
                       (course_name and course_name.lower() in course['course_name'].lower()):
                        matching_course = course
                        break
                
                if matching_course:
                    message = f"**{matching_course['course_code']} - {matching_course['course_name']}**\n\n"
                    message += f"Credits: {matching_course['credits']}\n"
                    message += f"Department: {matching_course['department']}\n"
                    if matching_course['description']:
                        message += f"Description: {matching_course['description']}\n"
                    
                    dispatcher.utter_message(text=message)
                else:
                    dispatcher.utter_message(text=f"I couldn't find information for the course you mentioned. Please check the course code or name.")
            else:
                dispatcher.utter_message(text="I'm having trouble accessing course information right now. Please try again later.")
                
        except Exception as e:
            dispatcher.utter_message(text="I encountered an error while fetching course information. Please try again.")
        
        return [SlotSet("course_code", None), SlotSet("course_name", None)]

class ActionGetSchedule(Action):
    def name(self) -> Text:
        return "action_get_schedule"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        try:
            # Get user metadata
            metadata = tracker.latest_message.get('metadata', {})
            user_id = metadata.get('user_id')
            
            if not user_id:
                dispatcher.utter_message(text="I need to verify your identity to show your schedule.")
                return []
            
            # Make API call to backend
            headers = {'Authorization': f'Bearer {metadata.get("token", "")}'}
            response = requests.get(f"{BACKEND_URL}/academic/schedule", headers=headers)
            
            if response.status_code == 200:
                schedule = response.json().get('schedule', [])
                
                if schedule:
                    message = "**Your Class Schedule:**\n\n"
                    current_day = None
                    
                    for class_item in schedule:
                        if current_day != class_item['day_of_week']:
                            current_day = class_item['day_of_week']
                            message += f"**{current_day}:**\n"
                        
                        message += f"• {class_item['course_code']} - {class_item['course_name']}\n"
                        message += f"  Time: {class_item['start_time']} - {class_item['end_time']}\n"
                        if class_item.get('room'):
                            message += f"  Room: {class_item['room']}\n"
                        message += "\n"
                    
                    dispatcher.utter_message(text=message)
                else:
                    dispatcher.utter_message(text="You don't have any classes scheduled at the moment.")
            else:
                dispatcher.utter_message(text="I'm having trouble accessing your schedule right now. Please try again later.")
                
        except Exception as e:
            dispatcher.utter_message(text="I encountered an error while fetching your schedule. Please try again.")
        
        return []

class ActionGetAssignments(Action):
    def name(self) -> Text:
        return "action_get_assignments"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        try:
            # Get user metadata
            metadata = tracker.latest_message.get('metadata', {})
            user_id = metadata.get('user_id')
            
            if not user_id:
                dispatcher.utter_message(text="I need to verify your identity to show your assignments.")
                return []
            
            # Make API call to backend
            headers = {'Authorization': f'Bearer {metadata.get("token", "")}'}
            response = requests.get(f"{BACKEND_URL}/academic/assignments", headers=headers)
            
            if response.status_code == 200:
                assignments = response.json().get('assignments', [])
                
                if assignments:
                    message = "**Your Assignments:**\n\n"
                    
                    for assignment in assignments[:5]:  # Show first 5 assignments
                        message += f"**{assignment['title']}**\n"
                        message += f"Course: {assignment['course']['course_code']} - {assignment['course']['course_name']}\n"
                        message += f"Due: {assignment['due_date'][:10]}\n"  # Show date only
                        if assignment.get('points'):
                            message += f"Points: {assignment['points']}\n"
                        message += "\n"
                    
                    if len(assignments) > 5:
                        message += f"... and {len(assignments) - 5} more assignments."
                    
                    dispatcher.utter_message(text=message)
                else:
                    dispatcher.utter_message(text="You don't have any assignments at the moment.")
            else:
                dispatcher.utter_message(text="I'm having trouble accessing your assignments right now. Please try again later.")
                
        except Exception as e:
            dispatcher.utter_message(text="I encountered an error while fetching your assignments. Please try again.")
        
        return []

class ActionGetLecturerInfo(Action):
    def name(self) -> Text:
        return "action_get_lecturer_info"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        lecturer_name = tracker.get_slot("lecturer_name")
        
        try:
            # Get user metadata
            metadata = tracker.latest_message.get('metadata', {})
            user_id = metadata.get('user_id')
            
            if not user_id:
                dispatcher.utter_message(text="I need to verify your identity to provide lecturer information.")
                return []
            
            # Make API call to backend
            headers = {'Authorization': f'Bearer {metadata.get("token", "")}'}
            response = requests.get(f"{BACKEND_URL}/academic/lecturers", headers=headers)
            
            if response.status_code == 200:
                lecturers = response.json().get('lecturers', [])
                
                if lecturer_name:
                    # Find specific lecturer
                    matching_lecturer = None
                    for lecturer in lecturers:
                        if lecturer_name.lower() in lecturer['name'].lower():
                            matching_lecturer = lecturer
                            break
                    
                    if matching_lecturer:
                        message = f"**{matching_lecturer['name']}**\n\n"
                        message += f"Email: {matching_lecturer['email']}\n"
                        if matching_lecturer.get('phone'):
                            message += f"Phone: {matching_lecturer['phone']}\n"
                        if matching_lecturer.get('office'):
                            message += f"Office: {matching_lecturer['office']}\n"
                        if matching_lecturer.get('office_hours'):
                            message += f"Office Hours: {matching_lecturer['office_hours']}\n"
                        
                        if matching_lecturer.get('courses'):
                            message += f"\nCourses:\n"
                            for course in matching_lecturer['courses']:
                                message += f"• {course['course_code']} - {course['course_name']}\n"
                        
                        dispatcher.utter_message(text=message)
                    else:
                        dispatcher.utter_message(text=f"I couldn't find information for {lecturer_name}. Please check the name.")
                else:
                    # Show all lecturers
                    if lecturers:
                        message = "**Available Lecturers:**\n\n"
                        for lecturer in lecturers[:5]:  # Show first 5
                            message += f"• {lecturer['name']} - {lecturer['email']}\n"
                        
                        if len(lecturers) > 5:
                            message += f"\n... and {len(lecturers) - 5} more lecturers."
                        
                        dispatcher.utter_message(text=message)
                    else:
                        dispatcher.utter_message(text="No lecturer information is available at the moment.")
            else:
                dispatcher.utter_message(text="I'm having trouble accessing lecturer information right now. Please try again later.")
                
        except Exception as e:
            dispatcher.utter_message(text="I encountered an error while fetching lecturer information. Please try again.")
        
        return [SlotSet("lecturer_name", None)]

class ActionDefaultFallback(Action):
    def name(self) -> Text:
        return "action_default_fallback"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        dispatcher.utter_message(text="I'm sorry, I didn't understand that. I can help you with:\n"
                                     "• Course information\n"
                                     "• Class schedules\n"
                                     "• Assignments and deadlines\n"
                                     "• Lecturer contact information\n"
                                     "• Exam schedules\n\n"
                                     "What would you like to know about?")
        return []
