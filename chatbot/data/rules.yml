version: "3.1"

rules:

- rule: Say goodbye anytime the user says goodbye
  steps:
  - intent: goodbye
  - action: utter_goodbye

- rule: Say 'I am a bot' anytime the user challenges
  steps:
  - intent: bot_challenge
  - action: utter_iamabot

- rule: Provide help when asked
  steps:
  - intent: ask_help
  - action: utter_help

- rule: Activate fallback when confidence is low
  steps:
  - intent: nlu_fallback
  - action: action_default_fallback

- rule: Course info with specific course code
  condition:
  - slot_was_set:
    - course_code: null
  steps:
  - intent: ask_course_info
    entities:
    - course_code
  - action: action_get_course_info

- rule: Lecturer info with specific lecturer name
  condition:
  - slot_was_set:
    - lecturer_name: null
  steps:
  - intent: ask_lecturer_info
    entities:
    - lecturer_name
  - action: action_get_lecturer_info

- rule: Ask for course code when not provided
  condition:
  - slot_was_set:
    - course_code: null
  steps:
  - intent: ask_course_info
  - action: utter_ask_course_code

- rule: Ask for lecturer name when not provided
  condition:
  - slot_was_set:
    - lecturer_name: null
  steps:
  - intent: ask_lecturer_info
  - action: utter_ask_lecturer_name
