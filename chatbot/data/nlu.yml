version: "3.1"

nlu:
- intent: greet
  examples: |
    - hey
    - hello
    - hi
    - hello there
    - good morning
    - good evening
    - moin
    - hey there
    - let's go
    - hey dude
    - goodmorning
    - goodevening
    - good afternoon

- intent: goodbye
  examples: |
    - cu
    - good by
    - cee you later
    - good night
    - bye
    - goodbye
    - have a nice day
    - see you around
    - bye bye
    - see you later
    - thanks, bye
    - talk to you later

- intent: affirm
  examples: |
    - yes
    - y
    - indeed
    - of course
    - that sounds good
    - correct
    - yes please
    - absolutely
    - sure
    - right
    - exactly
    - yep
    - yeah

- intent: deny
  examples: |
    - no
    - n
    - never
    - I don't think so
    - don't like that
    - no way
    - not really
    - nope
    - not interested
    - no thanks
    - I disagree

- intent: mood_great
  examples: |
    - perfect
    - great
    - amazing
    - feeling like a king
    - wonderful
    - I am feeling very good
    - I am great
    - I am amazing
    - I am going to save the world
    - super stoked
    - extremely good
    - so so perfect
    - so good
    - so perfect

- intent: mood_unhappy
  examples: |
    - my day was horrible
    - I am sad
    - I don't feel very well
    - I am disappointed
    - super sad
    - I'm so sad
    - sad
    - very sad
    - unhappy
    - not good
    - not very good
    - extremly sad
    - so saad
    - so sad

- intent: bot_challenge
  examples: |
    - are you a bot?
    - are you a human?
    - am I talking to a bot?
    - am I talking to a human?
    - what are you?
    - who are you?
    - are you real?
    - are you artificial?

- intent: ask_course_info
  examples: |
    - tell me about [CS101](course_code)
    - what is [Computer Science](course_name)?
    - course information for [MATH201](course_code)
    - I need info about [Physics](course_name)
    - describe [ENG101](course_code)
    - what can you tell me about [Biology](course_name)?
    - course details for [HIST301](course_code)
    - information about [Chemistry](course_name)
    - tell me about the [Data Structures](course_name) course
    - what is [CS102](course_code) about?
    - course description for [MATH101](course_code)
    - I want to know about [Statistics](course_name)

- intent: ask_schedule
  examples: |
    - what's my schedule?
    - show me my timetable
    - when do I have classes?
    - my class schedule
    - what classes do I have today?
    - when is my next class?
    - show me my schedule for [Monday](day_of_week)
    - what time is [CS101](course_code)?
    - when do I have [Math](course_name)?
    - my timetable for this week
    - class times
    - schedule for [Tuesday](day_of_week)

- intent: ask_assignments
  examples: |
    - what assignments do I have?
    - show me my homework
    - any assignments due?
    - what's due soon?
    - assignments for [CS101](course_code)
    - homework for [Math](course_name)
    - upcoming assignments
    - assignment deadlines
    - what do I need to submit?
    - pending assignments
    - homework due dates
    - assignments this week

- intent: ask_lecturer_info
  examples: |
    - who is [Dr. Smith](lecturer_name)?
    - contact info for [Professor Johnson](lecturer_name)
    - [Dr. Brown](lecturer_name)'s email
    - how to reach [Prof. Davis](lecturer_name)?
    - [Dr. Wilson](lecturer_name)'s office hours
    - where is [Professor Lee](lecturer_name)'s office?
    - [Dr. Taylor](lecturer_name)'s phone number
    - lecturer contact details
    - professor information
    - instructor details

- intent: ask_exam_schedule
  examples: |
    - when are my exams?
    - exam schedule
    - upcoming exams
    - exam dates
    - when is the [CS101](course_code) exam?
    - [Math](course_name) exam date
    - final exam schedule
    - midterm dates
    - exam timetable
    - test schedule
    - when do I have tests?

- intent: ask_deadlines
  examples: |
    - what's due this week?
    - upcoming deadlines
    - assignment due dates
    - what do I need to submit soon?
    - deadlines for [CS101](course_code)
    - when is the [project](assignment_name) due?
    - submission deadlines
    - due dates
    - what's coming up?

- intent: ask_office_hours
  examples: |
    - when are office hours?
    - [Dr. Smith](lecturer_name)'s office hours
    - when can I meet [Professor Johnson](lecturer_name)?
    - office hours for [CS101](course_code)
    - when is the professor available?
    - consultation hours
    - meeting times with lecturer

- intent: ask_help
  examples: |
    - help
    - what can you do?
    - how can you help me?
    - what information do you have?
    - what can I ask you?
    - help me
    - I need help
    - what are your capabilities?
    - what do you know?
