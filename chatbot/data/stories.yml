version: "3.1"

stories:

- story: happy path
  steps:
  - intent: greet
  - action: utter_greet
  - intent: mood_great
  - action: utter_happy

- story: sad path 1
  steps:
  - intent: greet
  - action: utter_greet
  - intent: mood_unhappy
  - action: utter_cheer_up
  - action: utter_did_that_help
  - intent: affirm
  - action: utter_happy

- story: sad path 2
  steps:
  - intent: greet
  - action: utter_greet
  - intent: mood_unhappy
  - action: utter_cheer_up
  - action: utter_did_that_help
  - intent: deny
  - action: utter_goodbye

- story: course info request
  steps:
  - intent: ask_course_info
  - action: action_get_course_info

- story: course info with course code
  steps:
  - intent: ask_course_info
    entities:
    - course_code: "CS101"
  - action: action_get_course_info

- story: schedule request
  steps:
  - intent: ask_schedule
  - action: action_get_schedule

- story: assignments request
  steps:
  - intent: ask_assignments
  - action: action_get_assignments

- story: lecturer info request
  steps:
  - intent: ask_lecturer_info
  - action: action_get_lecturer_info

- story: lecturer info with name
  steps:
  - intent: ask_lecturer_info
    entities:
    - lecturer_name: "<PERSON><PERSON> <PERSON>"
  - action: action_get_lecturer_info

- story: exam schedule request
  steps:
  - intent: ask_exam_schedule
  - action: action_get_exam_schedule

- story: deadlines request
  steps:
  - intent: ask_deadlines
  - action: action_get_deadlines

- story: help request
  steps:
  - intent: ask_help
  - action: utter_help

- story: bot challenge
  steps:
  - intent: bot_challenge
  - action: utter_iamabot

- story: interactive_story_1
  steps:
  - intent: greet
  - action: utter_greet
  - intent: ask_course_info
  - action: utter_ask_course_code
  - intent: ask_course_info
    entities:
    - course_code: "CS101"
  - action: action_get_course_info
  - intent: goodbye
  - action: utter_goodbye

- story: interactive_story_2
  steps:
  - intent: greet
  - action: utter_greet
  - intent: ask_schedule
  - action: action_get_schedule
  - intent: ask_assignments
  - action: action_get_assignments
  - intent: goodbye
  - action: utter_goodbye

- story: course info fallback
  steps:
  - intent: ask_course_info
  - action: action_get_course_info
  - intent: ask_course_info
  - action: utter_ask_course_code

- story: lecturer info fallback
  steps:
  - intent: ask_lecturer_info
  - action: action_get_lecturer_info
  - intent: ask_lecturer_info
  - action: utter_ask_lecturer_name

- story: general course inquiry
  steps:
  - intent: ask_course_info
  - action: utter_course_info_general

- story: general schedule inquiry
  steps:
  - intent: ask_schedule
  - action: utter_schedule_general

- story: general assignments inquiry
  steps:
  - intent: ask_assignments
  - action: utter_assignments_general

- story: general lecturer inquiry
  steps:
  - intent: ask_lecturer_info
  - action: utter_lecturer_info_general

- story: general exam inquiry
  steps:
  - intent: ask_exam_schedule
  - action: utter_exam_schedule_general
