version: "3.1"

intents:
  - greet
  - goodbye
  - affirm
  - deny
  - mood_great
  - mood_unhappy
  - bot_challenge
  - ask_course_info
  - ask_schedule
  - ask_assignments
  - ask_lecturer_info
  - ask_exam_schedule
  - ask_deadlines
  - ask_office_hours
  - ask_course_requirements
  - ask_grades
  - ask_enrollment
  - ask_help

entities:
  - course_code
  - course_name
  - lecturer_name
  - day_of_week
  - time
  - assignment_name
  - exam_type

slots:
  course_code:
    type: text
    influence_conversation: true
    mappings:
    - type: from_entity
      entity: course_code
  
  course_name:
    type: text
    influence_conversation: true
    mappings:
    - type: from_entity
      entity: course_name
  
  lecturer_name:
    type: text
    influence_conversation: true
    mappings:
    - type: from_entity
      entity: lecturer_name
  
  user_id:
    type: text
    influence_conversation: false
    mappings:
    - type: from_intent
      intent: greet
      value: null
  
  student_id:
    type: text
    influence_conversation: false
    mappings:
    - type: from_intent
      intent: greet
      value: null

responses:
  utter_greet:
  - text: "Hello! I'm your academic assistant. I can help you with course information, schedules, assignments, and lecturer contacts. What would you like to know?"
  - text: "Hi there! I'm here to help with your academic questions. How can I assist you today?"
  - text: "Welcome! I can provide information about courses, schedules, assignments, and more. What do you need help with?"

  utter_goodbye:
  - text: "Goodbye! Feel free to ask me anything about your academics anytime."
  - text: "See you later! I'm always here to help with your academic questions."
  - text: "Take care! Don't hesitate to reach out if you need academic assistance."

  utter_iamabot:
  - text: "I am an AI-powered academic assistant designed to help students with course information, schedules, assignments, and lecturer contacts."

  utter_help:
  - text: "I can help you with:\n• Course information and descriptions\n• Class schedules and timetables\n• Assignment deadlines and details\n• Lecturer contact information\n• Exam schedules\n• Office hours\n• Course requirements\n\nJust ask me about any of these topics!"

  utter_ask_course_code:
  - text: "Which course are you asking about? Please provide the course code (e.g., CS101, MATH201)."
  - text: "Could you specify the course code you're interested in?"

  utter_ask_lecturer_name:
  - text: "Which lecturer would you like information about? Please provide their name."
  - text: "Could you tell me the name of the lecturer you're asking about?"

  utter_default:
  - text: "I'm sorry, I didn't understand that. Could you please rephrase your question?"
  - text: "I'm not sure what you mean. Could you be more specific about what academic information you need?"
  - text: "I didn't catch that. I can help with courses, schedules, assignments, and lecturer information. What would you like to know?"

  utter_course_info_general:
  - text: "I can provide information about courses including descriptions, credits, prerequisites, and schedules. Which specific course would you like to know about?"

  utter_schedule_general:
  - text: "I can help you with your class schedule. Would you like to see your full timetable or information about a specific course?"

  utter_assignments_general:
  - text: "I can show you your assignments and deadlines. Would you like to see all assignments or those for a specific course?"

  utter_lecturer_info_general:
  - text: "I can provide lecturer contact information, office hours, and office locations. Which lecturer are you looking for?"

  utter_exam_schedule_general:
  - text: "I can show you your exam schedule including dates, times, and locations. Would you like to see all upcoming exams?"

actions:
  - action_get_course_info
  - action_get_schedule
  - action_get_assignments
  - action_get_lecturer_info
  - action_get_exam_schedule
  - action_get_deadlines
  - action_default_fallback

session_config:
  session_expiration_time: 60
  carry_over_slots_to_new_session: true
