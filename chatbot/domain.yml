version: "3.1"

intents:
  - greet
  - goodbye
  - ask_courses
  - ask_assignments
  - ask_schedule
  - ask_lecturers
  - bot_challenge

entities:
  - course_code
  - lecturer_name

slots:
  course_code:
    type: text
    influence_conversation: false
    mappings:
    - type: from_entity
      entity: course_code

responses:
  utter_greet:
  - text: "Hello! I'm your academic assistant. I can help you with courses, assignments, schedules, and lecturer information."

  utter_goodbye:
  - text: "Goodbye! Feel free to ask me about your academics anytime."

  utter_iamabot:
  - text: "I am an AI academic assistant here to help you with your studies."

actions:
  - action_get_courses
  - action_get_assignments
  - action_get_schedule
  - action_get_lecturers

session_config:
  session_expiration_time: 60
  carry_over_slots_to_new_session: true
