# AI-Powered Chatbot for Student Academic Support

A full-stack web application that provides AI-powered academic assistance to students with a clean, modern interface.

## 🚀 Features

- **AI-Powered NLP**: Rasa-based natural language processing with intent recognition
- **MongoDB Integration**: Persistent storage for academic data and conversations
- **Academic Support**: Handles course info, assignments, schedules, and lecturer contacts
- **Student Authentication**: Secure user registration and login with JWT
- **Chat History**: Conversation logging and retrieval from database
- **Responsive UI**: Modern React interface with beautiful animations
- **Real-time Chat**: Instant messaging with typing indicators
- **Fallback System**: Works with or without Rasa/MongoDB for maximum reliability

## 🏗️ Architecture

```
├── frontend/          # React + Vite frontend
├── backend/           # Flask API server with MongoDB integration
├── chatbot/           # Rasa NLP engine with custom actions
├── setup_db.py        # MongoDB setup and seeding script
└── run.sh            # Comprehensive startup script
```

## 🛠️ Technology Stack

- **Frontend**: React.js with Vite, Axios, Lucide Icons
- **Backend**: Flask (Python) with JWT authentication
- **Chatbot**: Rasa 3.6+ with custom actions
- **Database**: MongoDB with PyMongo
- **NLP**: Rasa NLU with DIET classifier
- **Styling**: Modern CSS with gradients and animations

## 🚀 Quick Start

1. **Prerequisites**:
   - Node.js 18+
   - Python 3.9+
   - MongoDB (optional - system works without it)
   - Rasa (optional - system has fallback responses)

2. **Install Dependencies**:
   ```bash
   # Frontend
   cd frontend && npm install

   # Backend
   cd backend && pip install -r requirements.txt

   # Rasa (optional)
   pip install rasa
   cd chatbot/actions && pip install -r requirements.txt
   ```

3. **Install MongoDB** (optional):
   ```bash
   # macOS with Homebrew
   brew install mongodb-community
   brew services start mongodb-community

   # Or use Docker
   docker run -d -p 27017:27017 --name mongodb mongo:6.0
   ```

4. **Start the Application**:
   ```bash
   ./run.sh
   ```

   The script will automatically:
   - Check and start MongoDB if available
   - Setup database with sample data
   - Train Rasa model if Rasa is installed
   - Start all services (Backend, Frontend, Rasa, Actions)

   Or start manually:
   ```bash
   # Terminal 1: Setup Database (if MongoDB is running)
   python3 setup_db.py

   # Terminal 2: Backend
   cd backend && python3 simple_app.py

   # Terminal 3: Frontend
   cd frontend && npm run dev

   # Terminal 4: Rasa (optional)
   cd chatbot && rasa train && rasa run --enable-api --cors "*"

   # Terminal 5: Rasa Actions (optional)
   cd chatbot && rasa run actions
   ```

## 📱 Usage

1. Open http://localhost:5173 or http://localhost:5174 in your browser
2. **Register** a new student account:
   - Email: <EMAIL>
   - Password: (minimum 6 characters)
   - Name, Student ID, Program, Year
3. **Login** and start chatting!
4. Ask about:
   - **Courses**: "What courses are available?"
   - **Assignments**: "What assignments do I have?"
   - **Schedule**: "What's my class schedule?"
   - **Lecturers**: "Who are my professors?"

## 🤖 AI Assistant Capabilities

- Natural language understanding
- Personalized responses using student name
- Academic data retrieval
- Intent recognition with confidence scoring
- Conversation context maintenance

## 📊 API Endpoints

- `GET /api/health` - Health check
- `POST /api/auth/register` - Student registration
- `POST /api/auth/login` - Student login
- `GET /api/auth/profile` - Get student profile
- `POST /api/chat` - Send message to chatbot
- `GET /api/chat/history` - Get chat history

## 📚 Sample Data

The system includes sample academic data:
- **Courses**: CS101, MATH201, ENG101
- **Assignments**: Programming and Calculus assignments
- **Schedule**: Weekly class timetable
- **Lecturers**: Contact information and office hours

## 🎯 System Status

✅ **Fully Operational** - Complete AI-powered system!
- **Frontend**: http://localhost:5173/5174 - Modern React interface
- **Backend**: http://localhost:5000 - Flask API with MongoDB integration
- **Rasa NLP**: http://localhost:5005 - AI-powered intent recognition
- **Actions**: http://localhost:5055 - Custom database actions
- **Database**: MongoDB with academic data
- **Authentication**: JWT-based secure login
- **Fallback**: Works without Rasa/MongoDB for maximum reliability

## 🤖 AI Capabilities

- **Intent Recognition**: Understands natural language queries
- **Entity Extraction**: Identifies courses, lecturers, dates
- **Context Management**: Maintains conversation context
- **Database Integration**: Real-time academic data retrieval
- **Confidence Scoring**: Provides response confidence levels
- **Fallback Responses**: Graceful degradation when services are unavailable
