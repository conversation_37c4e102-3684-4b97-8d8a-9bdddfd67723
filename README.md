# AI-Powered Chatbot for Student Academic Support

A full-stack web application that provides AI-powered academic assistance to students with a clean, modern interface.

## 🚀 Features

- **Smart AI Responses**: Rule-based intelligent responses for academic queries
- **Academic Support**: Handles course info, assignments, schedules, and lecturer contacts
- **Student Authentication**: Secure user registration and login
- **Chat History**: Conversation logging and retrieval
- **Responsive UI**: Modern React interface with beautiful animations
- **Real-time Chat**: Instant messaging with typing indicators

## 🏗️ Architecture

```
├── frontend/          # React + Vite frontend
├── backend/           # Flask API server (simplified)
└── run.sh            # Simple startup script
```

## 🛠️ Technology Stack

- **Frontend**: React.js with Vite, Axios, Lucide Icons
- **Backend**: Flask (Python) with JWT authentication
- **Storage**: In-memory (for demo purposes)
- **Styling**: Modern CSS with gradients and animations

## 🚀 Quick Start

1. **Prerequisites**:
   - Node.js 18+
   - Python 3.9+

2. **Install Dependencies**:
   ```bash
   # Frontend
   cd frontend && npm install

   # Backend
   cd backend && pip install -r requirements.txt
   ```

3. **Start the Application**:
   ```bash
   ./run.sh
   ```

   Or manually:
   ```bash
   # Terminal 1: Backend
   cd backend && python3 simple_app.py

   # Terminal 2: Frontend
   cd frontend && npm run dev
   ```

## 📱 Usage

1. Open http://localhost:5173 or http://localhost:5174 in your browser
2. **Register** a new student account:
   - Email: <EMAIL>
   - Password: (minimum 6 characters)
   - Name, Student ID, Program, Year
3. **Login** and start chatting!
4. Ask about:
   - **Courses**: "What courses are available?"
   - **Assignments**: "What assignments do I have?"
   - **Schedule**: "What's my class schedule?"
   - **Lecturers**: "Who are my professors?"

## 🤖 AI Assistant Capabilities

- Natural language understanding
- Personalized responses using student name
- Academic data retrieval
- Intent recognition with confidence scoring
- Conversation context maintenance

## 📊 API Endpoints

- `GET /api/health` - Health check
- `POST /api/auth/register` - Student registration
- `POST /api/auth/login` - Student login
- `GET /api/auth/profile` - Get student profile
- `POST /api/chat` - Send message to chatbot
- `GET /api/chat/history` - Get chat history

## 📚 Sample Data

The system includes sample academic data:
- **Courses**: CS101, MATH201, ENG101
- **Assignments**: Programming and Calculus assignments
- **Schedule**: Weekly class timetable
- **Lecturers**: Contact information and office hours

## 🎯 System Status

✅ **Fully Operational** - All features working perfectly!
- Frontend running on http://localhost:5174
- Backend API running on http://localhost:5000
- Authentication system active
- Chat functionality working
- Academic data responses implemented
